#!/usr/bin/env python3
"""
Recreate Videos with Slower FPS
Recreates comparison videos with lower FPS and fixes drift-straight/swing videos.
"""

import os
import sys
import argparse
import subprocess
from tqdm import tqdm

# Add current directory to path
sys.path.append('.')

from create_comparison_videos import (
    check_ffmpeg, 
    create_comparison_frames, 
    create_video_with_ffmpeg,
    process_inference_results
)


def recreate_videos_with_slow_fps(inference_dir, test_dir, output_dir, fps=15, quality='medium'):
    """Recreate all videos with slower FPS"""
    
    print(f"Recreating videos with FPS: {fps}")
    print(f"Quality: {quality}")
    
    # Check ffmpeg
    if not check_ffmpeg():
        raise RuntimeError("ffmpeg not found. Please install ffmpeg.")
    
    # Create new output directory for slow videos
    slow_videos_dir = os.path.join(output_dir, f'videos_fps_{fps}')
    os.makedirs(slow_videos_dir, exist_ok=True)
    
    # Process all videos with new settings
    video_results = process_inference_results(
        inference_dir, 
        test_dir, 
        slow_videos_dir, 
        fps, 
        quality
    )
    
    return video_results, slow_videos_dir


def recreate_specific_videos(inference_dir, test_dir, output_dir, video_names, fps=15, quality='medium'):
    """Recreate specific videos (like drift-straight and swing)"""
    
    print(f"Recreating specific videos: {video_names}")
    print(f"FPS: {fps}, Quality: {quality}")
    
    # Check ffmpeg
    if not check_ffmpeg():
        raise RuntimeError("ffmpeg not found. Please install ffmpeg.")
    
    # Create output directory
    specific_videos_dir = os.path.join(output_dir, f'specific_videos_fps_{fps}')
    os.makedirs(specific_videos_dir, exist_ok=True)
    
    video_results = {}
    
    for video_name in tqdm(video_names, desc="Processing specific videos"):
        try:
            print(f"\nProcessing: {video_name}")
            
            # Create comparison frames
            frame_result = create_comparison_frames(video_name, inference_dir, test_dir, specific_videos_dir)
            
            if frame_result is None:
                video_results[video_name] = {'status': 'failed', 'reason': 'frame_creation_failed'}
                continue
            
            # Create video
            video_output_path = os.path.join(specific_videos_dir, video_name, f"{video_name}_comparison_fps{fps}.mp4")
            
            if create_video_with_ffmpeg(frame_result['frames_dir'], video_output_path, fps, quality):
                video_results[video_name] = {
                    'status': 'success',
                    'video_path': video_output_path,
                    'num_frames': frame_result['num_frames'],
                    'fps': fps,
                    'quality': quality
                }
                print(f"✓ Created video: {video_output_path}")
            else:
                video_results[video_name] = {'status': 'failed', 'reason': 'video_creation_failed'}
                
        except Exception as e:
            video_results[video_name] = {'status': 'failed', 'reason': str(e)}
            print(f"✗ Failed to process {video_name}: {str(e)}")
    
    return video_results, specific_videos_dir


def main():
    parser = argparse.ArgumentParser(description='Recreate videos with slower FPS')
    parser.add_argument('--inference_dir', default='./test_results/inference_2025_05_29_01_48_30',
                       help='Directory containing inference results')
    parser.add_argument('--test_dir', default='./dataset/test',
                       help='Path to test dataset directory')
    parser.add_argument('--output_dir', default=None,
                       help='Output directory for videos (default: inference_dir)')
    parser.add_argument('--fps', type=int, default=15,
                       help='Video frame rate (default: 15 for slower videos)')
    parser.add_argument('--quality', choices=['low', 'medium', 'high'], default='medium',
                       help='Video quality (default: medium)')
    parser.add_argument('--mode', choices=['all', 'specific', 'failed'], default='failed',
                       help='Mode: all videos, specific videos, or only failed ones (default: failed)')
    parser.add_argument('--videos', nargs='+', default=['drift-straight', 'swing'],
                       help='Specific video names to process (default: drift-straight swing)')
    
    args = parser.parse_args()
    
    # Set output directory
    if args.output_dir is None:
        args.output_dir = args.inference_dir
    
    print(f"Recreating videos with slower FPS...")
    print(f"Inference dir: {args.inference_dir}")
    print(f"Test dir: {args.test_dir}")
    print(f"Output dir: {args.output_dir}")
    print(f"FPS: {args.fps}, Quality: {args.quality}")
    print(f"Mode: {args.mode}")
    
    if args.mode == 'all':
        # Recreate all videos with slower FPS
        print("\n🎬 Recreating ALL videos with slower FPS...")
        video_results, output_dir = recreate_videos_with_slow_fps(
            args.inference_dir, 
            args.test_dir, 
            args.output_dir, 
            args.fps, 
            args.quality
        )
        
    elif args.mode == 'specific':
        # Recreate specific videos
        print(f"\n🎯 Recreating SPECIFIC videos: {args.videos}")
        video_results, output_dir = recreate_specific_videos(
            args.inference_dir, 
            args.test_dir, 
            args.output_dir, 
            args.videos, 
            args.fps, 
            args.quality
        )
        
    elif args.mode == 'failed':
        # Recreate only the failed videos (drift-straight and swing)
        print(f"\n🔧 Recreating FAILED videos: {args.videos}")
        video_results, output_dir = recreate_specific_videos(
            args.inference_dir, 
            args.test_dir, 
            args.output_dir, 
            args.videos, 
            args.fps, 
            args.quality
        )
    
    # Save results
    import json
    results_file = os.path.join(output_dir, f'recreated_videos_fps{args.fps}_results.json')
    with open(results_file, 'w') as f:
        json.dump(video_results, f, indent=2)
    
    # Print summary
    successful = sum(1 for r in video_results.values() if r['status'] == 'success')
    failed = len(video_results) - successful
    
    print(f"\n=== Summary ===")
    print(f"Mode: {args.mode}")
    print(f"FPS: {args.fps}")
    print(f"Total videos processed: {len(video_results)}")
    print(f"Successful: {successful}")
    print(f"Failed: {failed}")
    print(f"Output directory: {output_dir}")
    print(f"Results saved to: {results_file}")
    
    if successful > 0:
        print(f"\n🎉 Successfully created {successful} videos with FPS {args.fps}!")
        print(f"📁 Videos location: {output_dir}")
        
        # List some created videos
        successful_videos = [name for name, result in video_results.items() 
                           if result['status'] == 'success']
        print(f"📊 Created videos:")
        for video_name in successful_videos[:5]:  # Show first 5
            result = video_results[video_name]
            rel_path = os.path.relpath(result['video_path'], output_dir)
            print(f"   - {rel_path}")
        if len(successful_videos) > 5:
            print(f"   ... and {len(successful_videos) - 5} more")
    
    if failed > 0:
        print(f"\n⚠️ {failed} videos failed:")
        failed_videos = [name for name, result in video_results.items() 
                        if result['status'] == 'failed']
        for video_name in failed_videos:
            reason = video_results[video_name].get('reason', 'unknown')
            print(f"   - {video_name}: {reason}")


if __name__ == "__main__":
    main()
