# 🎬 SDCNet Results Comparison Viewer

Una pagina HTML interattiva per confrontare visivamente i risultati delle estrapolazioni SDCNet tra diverse varianti del modello.

## 📋 Caratteristiche

- **Confronto visivo sincronizzato** tra 5 display:
  - Ground Truth (in alto)
  - SDCNet Original (No Skip)
  - SDCNet Original (With Skip)  
  - SDCNet Masked (No Skip)
  - SDCNet Masked (With Skip)

- **Controlli di navigazione**:
  - Selezione video dal dataset di test
  - Selezione risultati di inference
  - Navigazione frame-by-frame con frecce o tasti
  - Input diretto del numero di frame

- **G<PERSON>lia di overlay configurabile**:
  - Dimensioni da 1x1 (nessuna griglia) a 10x10
  - Colore personalizzabile
  - Sovrapposta su tutti i display per analisi visiva

- **Interfaccia responsive** che si adatta a diversi schermi

## 🚀 Installazione e Uso

### 1. Preparazione dei file

Assicurati che la struttura delle directory sia corretta:

```
sdcnet/
├── dataset/
│   └── test/
│       ├── X/                    # Frame di input
│       │   ├── video_name/
│       │   └── video_name_binary_ground_truth/
│       └── Y/                    # Ground truth
│           └── video_name/
├── test_results/
│   ├── inference_TIMESTAMP/      # Risultati originali no skip
│   ├── inference_TIMESTAMP_skip/ # Risultati originali con skip
│   ├── inference_TIMESTAMP_masked/      # Risultati masked no skip
│   └── inference_TIMESTAMP_masked_skip/ # Risultati masked con skip
├── sdcnet_comparison_viewer.html
├── viewer_server.py
├── viewer_config.json
└── README_Viewer.md
```

### 2. Avvio del server

```bash
# Avvia il server sulla porta predefinita (8000)
python viewer_server.py

# Oppure specifica una porta diversa
python viewer_server.py 8080
```

### 3. Apertura del viewer

Apri il browser e vai a:
```
http://localhost:8000/sdcnet_comparison_viewer.html
```

## 🎮 Controlli

### Selezione contenuto
1. **Video Selection**: Scegli il video da analizzare dal dropdown
2. **Inference Results**: Seleziona i risultati di inference da confrontare

### Navigazione frame
- **Previous/Next**: Bottoni per navigare frame-by-frame
- **Frame Input**: Inserisci direttamente il numero del frame
- **Tastiera**: 
  - `←` Freccia sinistra: frame precedente
  - `→` Freccia destra: frame successivo

### Griglia di overlay
- **Grid Size**: Slider da 1x1 a 10x10 per la dimensione della griglia
- **Grid Color**: Selettore colore per personalizzare la griglia
- La griglia viene sovrapposta su tutti i display per facilitare l'analisi visiva

## 📁 Struttura dei file

### `sdcnet_comparison_viewer.html`
Pagina HTML principale con interfaccia utente completa e logica JavaScript.

### `viewer_server.py`
Server HTTP Python che:
- Serve i file statici
- Fornisce API REST per ottenere liste di video e risultati di inference
- Gestisce la verifica dell'esistenza dei frame
- Supporta CORS per lo sviluppo

### `viewer_config.json`
File di configurazione che definisce:
- Percorsi delle directory
- Template dei percorsi per le diverse varianti del modello
- Lista dei video disponibili
- Impostazioni predefinite dell'interfaccia

## 🔧 API Endpoints

Il server fornisce i seguenti endpoint API:

- `GET /api/videos` - Lista dei video disponibili
- `GET /api/inference` - Lista dei risultati di inference
- `GET /api/frames?video=X&inference=Y` - Frame disponibili per video/inference
- `GET /api/check-image?path=X` - Verifica esistenza di un'immagine

## 🎨 Personalizzazione

### Modifica dei percorsi
Edita `viewer_config.json` per cambiare:
- Percorsi delle directory
- Template dei nomi dei file
- Lista dei video
- Impostazioni predefinite

### Aggiunta di nuove varianti del modello
Nel file di configurazione, aggiungi nuove entry in `model_variants` con:
- Titolo da mostrare
- Template del percorso
- Descrizione

### Stili CSS
Modifica la sezione `<style>` in `sdcnet_comparison_viewer.html` per personalizzare:
- Colori del tema
- Layout e dimensioni
- Animazioni e transizioni

## 🐛 Risoluzione problemi

### Il server non si avvia
- Verifica che Python 3 sia installato
- Controlla che la porta non sia già in uso
- Assicurati di essere nella directory corretta

### Video/frame non si caricano
- Verifica che i percorsi in `viewer_config.json` siano corretti
- Controlla che i file esistano nelle directory specificate
- Guarda la console del browser per errori JavaScript

### Griglia non appare
- Assicurati che la dimensione della griglia sia > 1x1
- Verifica che il colore della griglia non sia trasparente
- Controlla che le immagini siano caricate correttamente

## 📊 Formati supportati

- **Immagini**: PNG, JPG, JPEG
- **Naming convention**: 
  - Ground truth: `00001.png`, `00002.png`, etc.
  - Predizioni: `pred_00001.png`, `pred_00002.png`, etc. (per varianti masked)

## 🔄 Aggiornamenti futuri

Possibili miglioramenti:
- Supporto per video in formato MP4
- Zoom e pan sulle immagini
- Esportazione di confronti come immagini
- Metriche di qualità automatiche
- Supporto per batch comparison
- Integrazione con TensorBoard

## 📝 Note

- Il viewer è ottimizzato per frame di dimensioni tipiche dei dataset video
- La griglia è utile per analizzare deformazioni e allineamenti
- I risultati di inference devono seguire la convenzione di naming del progetto
- Il server è pensato per uso locale/sviluppo, non per produzione
