#!/usr/bin/env python3
"""
Test Inference Script for Original SDCNet (without masks)
Performs inference on the entire test set and saves predictions for video comparison.
"""

import os
import sys
import argparse
import cv2
import numpy as np
from PIL import Image
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from tqdm import tqdm
import json
from datetime import datetime
import glob

# Add current directory to path
sys.path.append('.')

from models.sdc_net2d import SDCNet2D
from datasets.frame_loader_original import FrameLoaderOriginal


def create_args():
    parser = argparse.ArgumentParser(description='SDCNet Original Inference')

    # Model parameters
    parser.add_argument('--model', default='SDCNet2D', type=str)
    parser.add_argument('--dataset', default='FrameLoaderOriginal', type=str)
    parser.add_argument('--sequence_length', default=2, type=int)
    parser.add_argument('--rgb_max', default=255.0, type=float)
    parser.add_argument('--flownet2_checkpoint',
                       default='./flownet2_pytorch/FlowNet2_checkpoint.pth.tar', type=str)

    # FlowNet2 parameters
    parser.add_argument('--fp16', action='store_true', help='Use fp16 for FlowNet2')

    # Inference parameters
    parser.add_argument('--checkpoint', required=True, type=str,
                       help='Path to trained model checkpoint')
    parser.add_argument('--test_dir', required=True, type=str,
                       help='Path to test dataset directory')
    parser.add_argument('--output_dir', default='./test_results', type=str,
                       help='Output directory for inference results')
    parser.add_argument('--batch_size', default=8, type=int,
                       help='Batch size for inference (default: 8)')
    parser.add_argument('--workers', default=2, type=int,
                       help='Number of data loading workers')

    # Dataset parameters
    parser.add_argument('--sample_rate', default=1, type=int)
    parser.add_argument('--crop_size', default=[256, 320], nargs=2, type=int)
    parser.add_argument('--start_index', default=0, type=int)
    parser.add_argument('--stride', default=64, type=int)

    # Device parameters
    parser.add_argument('--gpu', default=0, type=int, help='GPU device ID')

    # Output control
    parser.add_argument('--save_predictions', action='store_true', default=True,
                       help='Save prediction images')
    parser.add_argument('--save_comparisons', action='store_true', default=True,
                       help='Save side-by-side comparison images')
    parser.add_argument('--max_videos', default=None, type=int,
                       help='Limit number of videos to process (for testing)')

    return parser.parse_args()


def setup_device(args):
    """Setup GPU device"""
    if torch.cuda.is_available():
        device = torch.device(f'cuda:{args.gpu}')
        print(f"Using GPU {args.gpu}: {torch.cuda.get_device_name(args.gpu)}")
    else:
        device = torch.device('cpu')
        print("CUDA not available, using CPU")

    return device


def load_model(args, device):
    """Load trained model from checkpoint"""
    print(f"🔍 Loading original SDCNet model from: {args.checkpoint}")

    # Create model
    model = SDCNet2D(args)
    model = model.to(device)

    # Load checkpoint
    try:
        checkpoint = torch.load(args.checkpoint, map_location=device)

        # Handle different checkpoint formats
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
            epoch = checkpoint.get('epoch', 'unknown')
            print(f"✅ Loaded checkpoint from epoch {epoch}")
        else:
            model.load_state_dict(checkpoint)
            print(f"✅ Loaded checkpoint (legacy format)")

        # Check if training args are available
        if 'args' in checkpoint:
            training_args = checkpoint['args']
            print(f"📊 Training configuration found:")
            print(f"   - Batch size: {training_args.get('batch_size', 'unknown')}")
            print(f"   - Learning rate: {training_args.get('lr', 'unknown')}")
            print(f"   - Skip augmentation: {training_args.get('skip_augmentation', False)}")
        else:
            print(f"⚠️  No training args found in checkpoint")

    except Exception as e:
        print(f"❌ Failed to load model: {e}")
        return None

    model.eval()
    return model


# Removed unused functions - now using direct video processing like masks version


def get_frame_number_from_path(frame_path):
    """Extract frame number from file path, handling both 5 and 6 digit formats"""
    filename = os.path.basename(frame_path)
    name_without_ext = os.path.splitext(filename)[0]

    # Try to extract number from end of filename
    import re
    match = re.search(r'(\d{5,6})$', name_without_ext)
    if match:
        return int(match.group(1))

    # Fallback: try to find any number
    match = re.search(r'(\d+)', name_without_ext)
    if match:
        return int(match.group(1))

    return 0


def predict_video_sequence(model, video_name, test_dir, device, args):
    """Predict sequence for a single video using sliding window approach (like masks version)"""

    # Get frame files for this video
    video_dir = os.path.join(test_dir, 'X', video_name)
    if not os.path.exists(video_dir):
        print(f"❌ Video directory not found: {video_dir}")
        return []

    # Get all frame files
    frame_files = []
    for ext in ['*.png', '*.jpg', '*.jpeg']:
        frame_files.extend(glob.glob(os.path.join(video_dir, ext)))

    if not frame_files:
        print(f"❌ No frame files found in {video_dir}")
        return []

    # Sort frames by number
    frame_files.sort(key=get_frame_number_from_path)

    predictions = []

    # Process video in sliding window fashion (EXACTLY like masks version)
    # For SDCNet2D: we need sequence_length frames + 1 target
    for i in tqdm(range(len(frame_files) - args.sequence_length),
                  desc=f"Predicting {video_name}", leave=False):

        # Get sequence of frames
        sequence_frames = []

        # Load input frames (t-1, t) and target frame (t+1)
        for j in range(args.sequence_length + 1):  # +1 for target
            frame_idx = i + j

            # Load frame - EXACTLY like masks version
            frame = cv2.imread(frame_files[frame_idx])[..., :3]  # RGB only
            frame = torch.from_numpy(frame.transpose(2, 0, 1)).float()
            sequence_frames.append(frame)

        # Create input dict
        input_dict = {
            'image': [frame.unsqueeze(0).to(device) for frame in sequence_frames]
        }

        # Perform inference
        with torch.no_grad():
            losses, prediction, target = model(input_dict)

        # Convert prediction to numpy - EXACTLY like masks version
        pred_np = prediction.cpu().squeeze().numpy().transpose(1, 2, 0)
        pred_np = np.clip(pred_np, 0, 255).astype(np.uint8)

        predictions.append({
            'frame_idx': i + args.sequence_length,  # Index of predicted frame
            'prediction': pred_np,
            'frame_file': frame_files[i + args.sequence_length],
            'losses': {k: v.item() if torch.is_tensor(v) else v for k, v in losses.items()}
        })

    return predictions


def save_predictions(predictions, video_name, output_dir):
    """Save predictions for a video - EXACTLY like masks version"""
    video_output_dir = os.path.join(output_dir, video_name)
    os.makedirs(video_output_dir, exist_ok=True)

    saved_frames = []

    # Save individual frames
    for pred_data in predictions:
        frame_idx = pred_data['frame_idx']
        prediction = pred_data['prediction']

        # Get frame number from original file
        original_frame_file = pred_data['frame_file']
        frame_number = get_frame_number_from_path(original_frame_file)

        # Create filename with proper number of digits
        if frame_number < 100000:
            frame_filename = f"{frame_number:05d}.png"
        else:
            frame_filename = f"{frame_number:06d}.png"

        # Save prediction
        pred_path = os.path.join(video_output_dir, frame_filename)
        cv2.imwrite(pred_path, prediction)  # Save as BGR (cv2 default)

        saved_frames.append({
            'frame_number': frame_number,
            'filename': frame_filename,
            'path': pred_path
        })

    # Save metadata
    metadata = {
        'video_name': video_name,
        'num_predictions': len(predictions),
        'frame_indices': [p['frame_idx'] for p in predictions],
        'saved_frames': saved_frames,
        'average_losses': {}
    }

    # Calculate average losses
    if predictions:
        loss_keys = predictions[0]['losses'].keys()
        for key in loss_keys:
            avg_loss = np.mean([p['losses'][key] for p in predictions])
            metadata['average_losses'][key] = float(avg_loss)

    metadata_file = os.path.join(video_output_dir, 'metadata.json')
    with open(metadata_file, 'w') as f:
        json.dump(metadata, f, indent=2)

    print(f"✅ Saved {len(predictions)} predictions for {video_name}")
    return video_output_dir, saved_frames


def run_inference(model, output_dir, args, device):
    """Run inference on the entire dataset - EXACTLY like masks version"""
    print(f"🚀 Starting inference...")

    model.eval()

    # Get all video directories
    test_x_dir = os.path.join(args.test_dir, 'X')
    if not os.path.exists(test_x_dir):
        print(f"❌ Test X directory not found: {test_x_dir}")
        return {}

    video_dirs = [d for d in os.listdir(test_x_dir)
                  if os.path.isdir(os.path.join(test_x_dir, d))
                  and not d.endswith('_binary_ground_truth')]

    video_dirs.sort()

    print(f"📹 Found {len(video_dirs)} videos: {', '.join(video_dirs[:5])}{'...' if len(video_dirs) > 5 else ''}")

    # Limit videos if specified
    if args.max_videos:
        video_dirs = video_dirs[:args.max_videos]
        print(f"🔢 Limited to {len(video_dirs)} videos for testing")

    # Process each video
    all_results = {}
    total_frames = 0

    for video_idx, video_name in enumerate(video_dirs):
        print(f"\n📹 Processing video {video_idx + 1}/{len(video_dirs)}: {video_name}")

        try:
            # Generate predictions for this video
            predictions = predict_video_sequence(model, video_name, args.test_dir, device, args)

            if not predictions:
                print(f"⚠️  No predictions generated for {video_name}")
                all_results[video_name] = {
                    'status': 'no_predictions',
                    'count': 0
                }
                continue

            # Save predictions
            video_output_dir, saved_frames = save_predictions(predictions, video_name, output_dir)

            all_results[video_name] = {
                'output_dir': video_output_dir,
                'count': len(predictions),
                'frames': saved_frames,
                'status': 'success'
            }

            total_frames += len(predictions)

        except Exception as e:
            print(f"❌ Error processing video {video_name}: {str(e)}")
            all_results[video_name] = {
                'status': 'error',
                'error': str(e),
                'count': 0
            }

    # Save results summary
    results = {
        'total_videos': len(video_dirs),
        'successful_videos': len([r for r in all_results.values() if r['status'] == 'success']),
        'total_frames': total_frames,
        'video_results': all_results,
        'checkpoint': args.checkpoint,
        'test_dir': args.test_dir,
        'output_dir': output_dir,
        'timestamp': datetime.now().isoformat(),
        'model': 'SDCNet2D_Original'
    }

    results_file = os.path.join(output_dir, 'inference_results.json')
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)

    print(f"\n✅ Inference completed!")
    print(f"   - Total videos: {results['total_videos']}")
    print(f"   - Successful: {results['successful_videos']}")
    print(f"   - Total frames: {results['total_frames']}")
    print(f"   - Success rate: {results['successful_videos']/results['total_videos']*100:.1f}%")
    print(f"   - Results saved to: {results_file}")

    # Print video summary
    print(f"\n📊 Video Summary:")
    for video_name, info in all_results.items():
        if info['status'] == 'success':
            print(f"   - {video_name}: {info['count']} frames")
        else:
            print(f"   - {video_name}: {info['status']}")

    return results


def main():
    args = create_args()

    print("🚀 Original SDCNet Inference")
    print("=" * 50)
    print(f"Device: cuda:{args.gpu}" if torch.cuda.is_available() else "Device: cpu")

    # Setup device
    device = setup_device(args)

    # Load model
    model = load_model(args, device)
    if model is None:
        return

    # Create output directories
    timestamp = datetime.now().strftime("%Y_%m_%d_%H_%M_%S")
    output_dir = os.path.join(args.output_dir, f"inference_{timestamp}")
    os.makedirs(output_dir, exist_ok=True)

    print(f"📂 Output directory: {output_dir}")

    # Run inference (no DataLoader needed - direct video processing like masks version)
    results = run_inference(model, output_dir, args, device)

    print(f"\n🎬 Next steps:")
    print(f"1. Create comparison videos:")
    print(f"   python create_comparison_videos_original.py --inference_dir {output_dir}")
    print(f"2. Check results in: {output_dir}")


if __name__ == "__main__":
    main()
