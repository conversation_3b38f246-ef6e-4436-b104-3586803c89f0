#!/usr/bin/env python3
"""
Video Utilities for SDCNet Test Inference
Utility functions for video processing, frame extraction, and comparison creation.
"""

import os
import cv2
import numpy as np
import subprocess
import json
from typing import List, Tuple, Optional, Dict
import glob


def get_video_info(video_path: str) -> Dict:
    """Get video information using ffprobe"""
    try:
        cmd = [
            'ffprobe', '-v', 'quiet', '-print_format', 'json',
            '-show_format', '-show_streams', video_path
        ]
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        return json.loads(result.stdout)
    except (subprocess.CalledProcessError, FileNotFoundError, json.JSONDecodeError):
        return {}


def extract_frames_from_video(video_path: str, output_dir: str, 
                            start_frame: int = 0, num_frames: Optional[int] = None) -> List[str]:
    """Extract frames from video using ffmpeg"""
    os.makedirs(output_dir, exist_ok=True)
    
    # Build ffmpeg command
    cmd = ['ffmpeg', '-y', '-i', video_path]
    
    if start_frame > 0:
        cmd.extend(['-vf', f'select=gte(n\\,{start_frame})'])
    
    if num_frames is not None:
        cmd.extend(['-frames:v', str(num_frames)])
    
    output_pattern = os.path.join(output_dir, 'frame_%05d.png')
    cmd.append(output_pattern)
    
    try:
        subprocess.run(cmd, capture_output=True, check=True)
        # Return list of created frame files
        return sorted(glob.glob(os.path.join(output_dir, 'frame_*.png')))
    except subprocess.CalledProcessError:
        return []


def create_video_from_frames(frames_dir: str, output_path: str, 
                           fps: int = 30, pattern: str = "frame_%05d.png") -> bool:
    """Create video from frames using ffmpeg"""
    input_pattern = os.path.join(frames_dir, pattern)
    
    cmd = [
        'ffmpeg', '-y',
        '-framerate', str(fps),
        '-i', input_pattern,
        '-c:v', 'libx264',
        '-pix_fmt', 'yuv420p',
        '-crf', '23',
        output_path
    ]
    
    try:
        subprocess.run(cmd, capture_output=True, check=True)
        return True
    except subprocess.CalledProcessError:
        return False


def resize_frame(frame: np.ndarray, target_size: Tuple[int, int]) -> np.ndarray:
    """Resize frame to target size (width, height)"""
    return cv2.resize(frame, target_size)


def add_text_to_frame(frame: np.ndarray, text: str, position: Tuple[int, int] = (10, 30),
                     font_scale: float = 0.7, color: Tuple[int, int, int] = (255, 255, 255),
                     thickness: int = 2) -> np.ndarray:
    """Add text overlay to frame"""
    font = cv2.FONT_HERSHEY_SIMPLEX
    cv2.putText(frame, text, position, font, font_scale, color, thickness)
    return frame


def create_side_by_side_comparison(left_frame: np.ndarray, right_frame: np.ndarray,
                                 left_label: str = "Original", right_label: str = "Predicted",
                                 add_labels: bool = True) -> np.ndarray:
    """Create side-by-side comparison of two frames"""
    # Ensure frames have same height
    h1, w1 = left_frame.shape[:2]
    h2, w2 = right_frame.shape[:2]
    
    target_height = max(h1, h2)
    
    # Resize frames to same height
    if h1 != target_height:
        aspect_ratio = w1 / h1
        new_width = int(target_height * aspect_ratio)
        left_frame = cv2.resize(left_frame, (new_width, target_height))
    
    if h2 != target_height:
        aspect_ratio = w2 / h2
        new_width = int(target_height * aspect_ratio)
        right_frame = cv2.resize(right_frame, (new_width, target_height))
    
    # Add labels if requested
    if add_labels:
        left_frame = add_text_to_frame(left_frame.copy(), left_label)
        right_frame = add_text_to_frame(right_frame.copy(), right_label)
    
    # Concatenate horizontally
    comparison = np.hstack([left_frame, right_frame])
    return comparison


def create_grid_comparison(frames: List[np.ndarray], labels: List[str] = None,
                         grid_size: Tuple[int, int] = None) -> np.ndarray:
    """Create grid comparison of multiple frames"""
    if not frames:
        return np.array([])
    
    num_frames = len(frames)
    
    # Auto-determine grid size if not provided
    if grid_size is None:
        cols = int(np.ceil(np.sqrt(num_frames)))
        rows = int(np.ceil(num_frames / cols))
        grid_size = (rows, cols)
    
    rows, cols = grid_size
    
    # Get frame dimensions (use first frame as reference)
    h, w = frames[0].shape[:2]
    
    # Resize all frames to same size
    resized_frames = []
    for frame in frames:
        if frame.shape[:2] != (h, w):
            frame = cv2.resize(frame, (w, h))
        resized_frames.append(frame)
    
    # Add labels if provided
    if labels:
        for i, (frame, label) in enumerate(zip(resized_frames, labels)):
            resized_frames[i] = add_text_to_frame(frame.copy(), label)
    
    # Create grid
    grid_rows = []
    for row in range(rows):
        row_frames = []
        for col in range(cols):
            idx = row * cols + col
            if idx < len(resized_frames):
                row_frames.append(resized_frames[idx])
            else:
                # Fill with black frame
                row_frames.append(np.zeros_like(resized_frames[0]))
        
        if row_frames:
            grid_rows.append(np.hstack(row_frames))
    
    if grid_rows:
        return np.vstack(grid_rows)
    else:
        return np.array([])


def calculate_frame_metrics(original: np.ndarray, predicted: np.ndarray) -> Dict[str, float]:
    """Calculate quality metrics between original and predicted frames"""
    # Convert to float for calculations
    orig_float = original.astype(np.float64)
    pred_float = predicted.astype(np.float64)
    
    # Mean Squared Error
    mse = np.mean((orig_float - pred_float) ** 2)
    
    # Peak Signal-to-Noise Ratio
    if mse == 0:
        psnr = float('inf')
    else:
        max_pixel = 255.0
        psnr = 20 * np.log10(max_pixel / np.sqrt(mse))
    
    # Structural Similarity Index (simplified version)
    # This is a basic implementation - for full SSIM, use skimage.metrics.structural_similarity
    mu1 = np.mean(orig_float)
    mu2 = np.mean(pred_float)
    sigma1_sq = np.var(orig_float)
    sigma2_sq = np.var(pred_float)
    sigma12 = np.mean((orig_float - mu1) * (pred_float - mu2))
    
    c1 = (0.01 * 255) ** 2
    c2 = (0.03 * 255) ** 2
    
    ssim = ((2 * mu1 * mu2 + c1) * (2 * sigma12 + c2)) / \
           ((mu1**2 + mu2**2 + c1) * (sigma1_sq + sigma2_sq + c2))
    
    # Mean Absolute Error
    mae = np.mean(np.abs(orig_float - pred_float))
    
    return {
        'mse': float(mse),
        'psnr': float(psnr),
        'ssim': float(ssim),
        'mae': float(mae)
    }


def create_metrics_overlay(frame: np.ndarray, metrics: Dict[str, float]) -> np.ndarray:
    """Add metrics overlay to frame"""
    frame_with_overlay = frame.copy()
    
    # Create semi-transparent overlay
    overlay = frame_with_overlay.copy()
    h, w = frame.shape[:2]
    
    # Draw background rectangle
    cv2.rectangle(overlay, (w - 200, 10), (w - 10, 120), (0, 0, 0), -1)
    
    # Blend with original
    alpha = 0.7
    cv2.addWeighted(overlay, alpha, frame_with_overlay, 1 - alpha, 0, frame_with_overlay)
    
    # Add text
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 0.4
    color = (255, 255, 255)
    thickness = 1
    
    y_offset = 30
    for key, value in metrics.items():
        text = f"{key.upper()}: {value:.2f}"
        cv2.putText(frame_with_overlay, text, (w - 190, y_offset), 
                   font, font_scale, color, thickness)
        y_offset += 20
    
    return frame_with_overlay


def batch_process_frames(input_dir: str, output_dir: str, 
                        process_func, file_pattern: str = "*.png") -> List[str]:
    """Apply processing function to all frames in directory"""
    os.makedirs(output_dir, exist_ok=True)
    
    input_files = sorted(glob.glob(os.path.join(input_dir, file_pattern)))
    output_files = []
    
    for input_file in input_files:
        filename = os.path.basename(input_file)
        output_file = os.path.join(output_dir, filename)
        
        # Load frame
        frame = cv2.imread(input_file)
        if frame is not None:
            # Apply processing function
            processed_frame = process_func(frame)
            
            # Save processed frame
            cv2.imwrite(output_file, processed_frame)
            output_files.append(output_file)
    
    return output_files


def check_video_tools() -> Dict[str, bool]:
    """Check availability of video processing tools"""
    tools = {}
    
    # Check ffmpeg
    try:
        subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True)
        tools['ffmpeg'] = True
    except (subprocess.CalledProcessError, FileNotFoundError):
        tools['ffmpeg'] = False
    
    # Check ffprobe
    try:
        subprocess.run(['ffprobe', '-version'], capture_output=True, check=True)
        tools['ffprobe'] = True
    except (subprocess.CalledProcessError, FileNotFoundError):
        tools['ffprobe'] = False
    
    return tools


def get_optimal_fps(frame_count: int, duration_seconds: float = 10.0) -> int:
    """Calculate optimal FPS for given frame count and desired duration"""
    fps = max(1, int(frame_count / duration_seconds))
    
    # Common FPS values
    common_fps = [24, 25, 30, 60]
    
    # Find closest common FPS
    closest_fps = min(common_fps, key=lambda x: abs(x - fps))
    
    return closest_fps
