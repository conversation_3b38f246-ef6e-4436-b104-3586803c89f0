#!/usr/bin/env python3
"""
Frame loader for original SDCNet (without masks) with skip augmentation support.
Based on the original frame_loader.py but enhanced with skip frame augmentation capability.
"""

import os
import cv2
import numpy as np
import torch
import torch.utils.data as data
from datasets.dataset_utils import StaticRandomCrop


class FrameLoaderOriginal(data.Dataset):
    def __init__(self, args, root, is_training=False, transform=None):
        
        self.is_training = is_training
        self.transform = transform
        self.chsize = 3

        # carry over command line arguments
        assert args.sequence_length > 1, 'sequence length must be > 1'
        self.sequence_length = args.sequence_length

        assert args.sample_rate > 0, 'sample rate must be > 0'
        self.sample_rate = args.sample_rate

        self.crop_size = args.crop_size
        self.start_index = args.start_index
        self.stride = args.stride

        # Skip augmentation for data augmentation (t-2,t -> t+2)
        self.skip_augmentation = getattr(args, 'skip_augmentation', False) and is_training
        print(f"Skip augmentation: {'enabled' if self.skip_augmentation else 'disabled'}")

        print("root path provided: {}".format(root))
        assert (os.path.exists(root))
        if self.is_training:
            self.start_index = 0

        # collect frame lists
        self.ref = self.collect_filelist(root)

        # Calculate counts for consecutive sequences (original behavior)
        consecutive_counts = [((len(el) - self.sequence_length) // (self.sample_rate)) for el in self.ref]
        self.consecutive_total = np.sum(consecutive_counts)
        self.consecutive_cum_sum = list(np.cumsum([0] + [el for el in consecutive_counts]))

        if self.skip_augmentation:
            # Calculate counts for skip sequences (t-2, t, t+2)
            # Need at least 5 frames for skip sequences (indices 0,2,4)
            skip_counts = [max(0, ((len(el) - 4) // (self.sample_rate))) for el in self.ref]
            self.skip_total = np.sum(skip_counts)
            self.skip_cum_sum = list(np.cumsum([0] + [el for el in skip_counts]))
            
            self.total = self.consecutive_total + self.skip_total
            print(f"Dataset size - Consecutive: {self.consecutive_total}, Skip: {self.skip_total}, Total: {self.total}")
        else:
            self.total = self.consecutive_total
            print(f"Dataset size - Total: {self.total}")

    def collect_filelist(self, root):
        """
        Collect frame file lists from the dataset structure.
        Expected structure:
        root/
        ├── X/
        │   ├── video_name/           # Frame directory
        └── Y/
            ├── video_name/           # Target frame directory (optional, can be same as X)
        """
        datasets = []

        # Look for X directory (input frames)
        x_dir = os.path.join(root, 'X')
        
        if not os.path.exists(x_dir):
            raise ValueError(f"Expected X directory in {root}")

        # Get all video directories in X
        video_dirs = [d for d in os.listdir(x_dir) 
                     if os.path.isdir(os.path.join(x_dir, d)) and not d.endswith('_binary_ground_truth')]

        for video_name in sorted(video_dirs):
            frame_dir = os.path.join(x_dir, video_name)
            
            # Get all frame files
            frame_files = []
            for ext in ['*.png', '*.jpg', '*.jpeg']:
                import glob
                frame_files.extend(glob.glob(os.path.join(frame_dir, ext)))
            
            frame_files = sorted(frame_files)
            
            if len(frame_files) < self.sequence_length + 1:
                print(f"Warning: {video_name} has only {len(frame_files)} frames, skipping")
                continue

            datasets.append(frame_files)
            print(f"Added video: {video_name} with {len(frame_files)} frames")

        print(f"Found {len(datasets)} valid video sequences")
        return datasets

    def __len__(self):
        return self.total

    def __getitem__(self, index):
        # adjust index
        index = len(self) + index if index < 0 else index
        index = index + self.start_index

        # Determine if this is a consecutive or skip sequence
        if self.skip_augmentation and index >= self.consecutive_total:
            # This is a skip sequence (t-2, t, t+2)
            skip_index = index - self.consecutive_total
            dataset_index = np.searchsorted(self.skip_cum_sum, skip_index + 1)
            local_index = self.sample_rate * (skip_index - self.skip_cum_sum[np.maximum(0, dataset_index - 1)])

            image_list = self.ref[dataset_index - 1]
            
            # Get file paths for skip sequence: t-2, t, t+2 (indices 0, 2, 4)
            input_files = [
                image_list[local_index],      # t-2
                image_list[local_index + 2],  # t
                image_list[local_index + 4]   # t+2 (target)
            ]
            
            sequence_type = "skip"
        else:
            # This is a consecutive sequence (t-1, t, t+1)
            consecutive_index = index if not self.skip_augmentation else index
            dataset_index = np.searchsorted(self.consecutive_cum_sum, consecutive_index + 1)
            local_index = self.sample_rate * (consecutive_index - self.consecutive_cum_sum[np.maximum(0, dataset_index - 1)])

            image_list = self.ref[dataset_index - 1]
            
            # Get file paths for consecutive sequence: t-1, t, t+1
            input_files = [image_list[local_index + offset] for offset in range(self.sequence_length + 1)]
            
            sequence_type = "consecutive"

        # reverse image order with p=0.5 (DISABLED for clearer visualization)
        # if self.is_training and torch.randint(0, 2, (1,)).item():
        #     input_files = input_files[::-1]

        # Load images
        images = [cv2.imread(imfile)[..., :self.chsize] for imfile in input_files]

        # Pad images along height and width to fit them evenly into models.
        input_shape = images[0].shape[:2]
        height, width = input_shape
        
        if (height % self.stride) != 0:
            padded_height = (height // self.stride + 1) * self.stride
            images = [np.pad(im, ((0, padded_height - height), (0, 0), (0, 0)), 'reflect') for im in images]

        if (width % self.stride) != 0:
            padded_width = (width // self.stride + 1) * self.stride
            images = [np.pad(im, ((0, 0), (0, padded_width - width), (0, 0)), 'reflect') for im in images]

        # StaticRandomCrop placed after the padding
        if self.is_training:
            cropper = StaticRandomCrop(self.crop_size, input_shape)
            images = map(cropper, images)

        input_images = [torch.from_numpy(im.transpose(2, 0, 1)).float() for im in images]
        
        output_dict = {
            'image': input_images, 
            'ishape': input_shape, 
            'input_files': input_files,
            'sequence_type': sequence_type
        }

        return output_dict
