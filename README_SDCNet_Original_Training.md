# SDCNet Original Training System

## 🎯 Overview

This system provides comprehensive training capabilities for the **original SDCNet2D model** (without masks) to enable fair comparison with the mask-enhanced version. The system includes both standard consecutive frame training and skip frame augmentation.

## 🆚 Comparison: Original vs Masks

| Aspect | SDCNet Original | SDCNet with Masks |
|--------|----------------|-------------------|
| **Input Frames** | 2 (t-1, t) | 2 (t-1, t) |
| **Input Masks** | 0 | 3 (t-1, t, t+1) |
| **Output** | 1 frame (t+1) | 1 frame (t+1) |
| **Input Channels** | 8 (6 RGB + 2 flow) | 11 (6 RGB + 3 mask + 2 flow) |
| **Dataset Class** | `FrameLoaderOriginal` | `FrameLoaderWithMasks` |
| **Model Class** | `SDCNet2D` | `SDCNet2DWithMasks` |

## 📁 Files Created

### Core Components
- **`datasets/frame_loader_original.py`** - Dataset loader for original SDCNet with skip augmentation support
- **`train_sdcnet_original.py`** - Full training script with GPU support, early stopping, and monitoring
- **`train_sdcnet_original_quick_test.py`** - Quick test script to verify setup
- **`test_sdcnet_original.py`** - Comprehensive test suite
- **`run_training_sdcnet_original.bat`** - Windows batch script for easy training

### Updated Files
- **`datasets/__init__.py`** - Added import for `FrameLoaderOriginal`

## 🚀 Quick Start

### Step 1: Test Your Setup
```bash
# Run comprehensive tests
python test_sdcnet_original.py --dataset_path ./dataset/train

# Quick training test (2 epochs, minimal batches)
python train_sdcnet_original_quick_test.py \
  --train_file ./dataset/train \
  --val_file ./dataset/val \
  --gpu 0
```

### Step 2: Standard Training (Consecutive Frames Only)
```bash
python train_sdcnet_original.py \
  --train_file ./dataset/train \
  --val_file ./dataset/val \
  --epochs 100 \
  --batch_size 4 \
  --lr 0.0001 \
  --name sdcnet_original_standard \
  --gpu 0
```

### Step 3: Training with Skip Augmentation
```bash
python train_sdcnet_original.py \
  --skip_augmentation \
  --train_file ./dataset/train \
  --val_file ./dataset/val \
  --epochs 100 \
  --batch_size 4 \
  --lr 0.0001 \
  --name sdcnet_original_with_skip \
  --gpu 0
```

### Step 4: Windows Users - Use Batch Script
```cmd
# Double-click or run from command prompt
run_training_sdcnet_original.bat
```

## 🔧 Training Features

### Enhanced Training Pipeline
- ✅ **GPU Support**: Automatic CUDA detection and device management
- ✅ **Progress Monitoring**: Real-time tqdm progress bars
- ✅ **Early Stopping**: Patience-based stopping with best model restoration
- ✅ **Visual Monitoring**: Inference samples saved at batches 1, 1000, 2000
- ✅ **Comprehensive Logging**: Detailed loss statistics after each epoch
- ✅ **Smart Checkpointing**: Regular saves + best model tracking
- ✅ **Resume Capability**: Continue training from any checkpoint

### Skip Frame Augmentation
- **Standard sequences**: `t-1, t → t+1` (consecutive frames)
- **Skip sequences**: `t-2, t → t+2` (1-frame skip)
- **Dataset size**: Nearly doubles with skip augmentation
- **Better performance**: Improved handling of fast motion scenarios

## 📊 Expected Training Results

### Dataset Size Comparison
```
Without Skip Augmentation:
- Training samples: ~X sequences
- Validation samples: ~Y sequences

With Skip Augmentation:
- Training samples: ~2X sequences (consecutive + skip)
- Validation samples: ~Y sequences (validation uses consecutive only)
```

### Training Time
- **Standard training**: ~1-2 hours per epoch (depending on dataset size)
- **With skip augmentation**: ~2-4 hours per epoch (nearly double dataset)

## 🎛️ Training Parameters

### Recommended Settings
```bash
--epochs 100              # Sufficient for convergence
--batch_size 4            # Good balance of memory and performance
--lr 0.0001              # Stable learning rate
--patience 10            # Early stopping patience
--save_freq 5            # Save checkpoint every 5 epochs
--workers 4              # Data loading workers
```

### Memory Requirements
- **Batch size 4**: ~6-8 GB GPU memory
- **Batch size 2**: ~3-4 GB GPU memory (for smaller GPUs)

## 📈 Monitoring Training

### Real-time Monitoring
- **Progress bars**: Show current batch progress and loss values
- **Epoch statistics**: Comprehensive loss breakdown after each epoch
- **Early stopping**: Automatic detection of training plateau

### Visual Monitoring
- **Inference samples**: Saved at specific batches (1, 1000, 2000)
- **Comparison images**: Input | Prediction | Target side-by-side
- **Timestamped folders**: Organized by training session

### Checkpoint Management
- **Regular checkpoints**: Every 5 epochs (configurable)
- **Best model**: Automatically saved when validation improves
- **Resume training**: Continue from any saved checkpoint

## 🔍 Comparison with Mask Version

### For Fair Comparison
1. **Use same dataset**: Ensure both models train on identical frame sequences
2. **Same hyperparameters**: Use identical learning rates, batch sizes, etc.
3. **Same training duration**: Train for same number of epochs or until convergence
4. **Same evaluation metrics**: Use identical test sets and metrics

### Expected Differences
- **Training time**: Original should be faster (fewer input channels)
- **Memory usage**: Original should use less GPU memory
- **Performance**: Masks version expected to perform better on complex scenes

## 🛠️ Troubleshooting

### Common Issues

#### Dataset Loading Errors
```bash
# Check dataset structure
python test_sdcnet_original.py --dataset_path ./dataset/train
```

#### GPU Memory Issues
```bash
# Reduce batch size
--batch_size 2
--val_batch_size 1
```

#### FlowNet2 Checkpoint Missing
```bash
# Ensure FlowNet2 checkpoint exists
ls ./flownet2_pytorch/FlowNet2_checkpoint.pth.tar
```

### Performance Issues
- **Slow training**: Reduce `--workers` if CPU is bottleneck
- **Memory errors**: Reduce `--batch_size`
- **Disk space**: Monitor checkpoint directory size

## 📋 Training Checklist

Before starting training:
- [ ] Dataset properly structured in `./dataset/train/` and `./dataset/val/`
- [ ] FlowNet2 checkpoint available at `./flownet2_pytorch/FlowNet2_checkpoint.pth.tar`
- [ ] GPU available and sufficient memory
- [ ] Quick test completed successfully
- [ ] Sufficient disk space for checkpoints (~1GB per checkpoint)

## 🎯 Next Steps

After training completion:
1. **Evaluate models**: Compare original vs mask-enhanced performance
2. **Analyze results**: Study loss curves and visual outputs
3. **Test inference**: Run inference on test sequences
4. **Create comparisons**: Generate side-by-side comparison videos

## 📞 Support

If you encounter issues:
1. Run the test suite: `python test_sdcnet_original.py`
2. Try quick test first: `python train_sdcnet_original_quick_test.py`
3. Check dataset structure and file paths
4. Verify GPU availability and memory
