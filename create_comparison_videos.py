#!/usr/bin/env python3
"""
Create Comparison Videos Script
Creates side-by-side comparison videos between original and predicted frames using ffmpeg.
"""

import os
import sys
import argparse
import json
import subprocess
import cv2
import numpy as np
from tqdm import tqdm
import glob
from datetime import datetime


def check_ffmpeg():
    """Check if ffmpeg is available"""
    try:
        subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False


def create_side_by_side_frame(original_path, predicted_path, output_path, add_labels=True):
    """Create a side-by-side comparison frame"""
    # Load images
    original = cv2.imread(original_path)
    predicted = cv2.imread(predicted_path)

    if original is None or predicted is None:
        return False

    # Ensure same size
    h, w = original.shape[:2]
    predicted = cv2.resize(predicted, (w, h))

    # Add labels if requested
    if add_labels:
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.7
        color = (255, 255, 255)
        thickness = 2

        # Add "Original" label
        cv2.putText(original, "Original", (10, 30), font, font_scale, color, thickness)

        # Add "Predicted" label
        cv2.putText(predicted, "Predicted", (10, 30), font, font_scale, color, thickness)

    # Create side-by-side image
    comparison = np.hstack([original, predicted])

    # Save comparison frame
    cv2.imwrite(output_path, comparison)
    return True


def create_comparison_frames(video_name, inference_dir, test_dir, output_dir):
    """Create comparison frames for a single video"""
    print(f"Creating comparison frames for: {video_name}")

    # Paths
    video_inference_dir = os.path.join(inference_dir, video_name)
    video_output_dir = os.path.join(output_dir, video_name)
    frames_dir = os.path.join(video_output_dir, "comparison_frames")

    os.makedirs(frames_dir, exist_ok=True)

    # Load metadata
    metadata_file = os.path.join(video_inference_dir, 'metadata.json')
    if not os.path.exists(metadata_file):
        print(f"Metadata not found for {video_name}")
        return None

    with open(metadata_file, 'r') as f:
        metadata = json.load(f)

    # Get original frames from test set
    original_frames_dir = os.path.join(test_dir, "Y", video_name)
    if not os.path.exists(original_frames_dir):
        print(f"Original frames not found: {original_frames_dir}")
        return None

    # Detect frame numbering format by checking existing files
    sample_files = os.listdir(original_frames_dir)
    sample_files = [f for f in sample_files if f.endswith('.png')]

    if not sample_files:
        print(f"No PNG files found in {original_frames_dir}")
        return None

    # Determine numbering format (5 digits vs 6 digits)
    sample_file = sample_files[0]
    if sample_file.startswith('000000'):
        frame_format = "{:06d}.png"  # 6 digits (drift-straight, swing)
        print(f"Using 6-digit format for {video_name}")
    else:
        frame_format = "{:05d}.png"  # 5 digits (standard)
        print(f"Using 5-digit format for {video_name}")

    # Create comparison frames
    frame_indices = metadata['frame_indices']
    comparison_frames = []

    for frame_idx in tqdm(frame_indices, desc=f"Creating frames for {video_name}", leave=False):
        # Paths for original and predicted frames
        original_frame = os.path.join(original_frames_dir, frame_format.format(frame_idx))
        predicted_frame = os.path.join(video_inference_dir, f"pred_{frame_idx:05d}.png")
        comparison_frame = os.path.join(frames_dir, f"comparison_{frame_idx:05d}.png")

        # Check if files exist
        if not os.path.exists(original_frame):
            print(f"Original frame not found: {original_frame}")
            continue

        if not os.path.exists(predicted_frame):
            print(f"Predicted frame not found: {predicted_frame}")
            continue

        # Create comparison frame
        if create_side_by_side_frame(original_frame, predicted_frame, comparison_frame):
            comparison_frames.append(comparison_frame)

    if not comparison_frames:
        print(f"No comparison frames created for {video_name}")
        return None

    return {
        'frames_dir': frames_dir,
        'num_frames': len(comparison_frames),
        'frame_files': comparison_frames
    }


def create_video_with_ffmpeg(frames_dir, output_video_path, fps=30, quality='high'):
    """Create video from frames using ffmpeg"""
    if not check_ffmpeg():
        raise RuntimeError("ffmpeg not found. Please install ffmpeg.")

    # Quality settings
    quality_settings = {
        'high': ['-crf', '18', '-preset', 'slow'],
        'medium': ['-crf', '23', '-preset', 'medium'],
        'low': ['-crf', '28', '-preset', 'fast']
    }

    # Input pattern for frames
    input_pattern = os.path.join(frames_dir, "comparison_%05d.png")

    # FFmpeg command
    cmd = [
        'ffmpeg',
        '-y',  # Overwrite output file
        '-framerate', str(fps),
        '-i', input_pattern,
        '-c:v', 'libx264',
        '-pix_fmt', 'yuv420p',
    ] + quality_settings.get(quality, quality_settings['medium']) + [
        output_video_path
    ]

    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        return True
    except subprocess.CalledProcessError as e:
        print(f"FFmpeg error: {e.stderr}")
        return False


def process_inference_results(inference_dir, test_dir, output_dir, fps=30, quality='high'):
    """Process all inference results and create comparison videos"""
    # Load inference results
    results_file = os.path.join(inference_dir, 'inference_results.json')
    if not os.path.exists(results_file):
        raise FileNotFoundError(f"Inference results not found: {results_file}")

    with open(results_file, 'r') as f:
        inference_results = json.load(f)

    # Filter successful videos
    successful_videos = [name for name, result in inference_results.items()
                        if result['status'] == 'success']

    print(f"Found {len(successful_videos)} successful inference results")

    # Process each video
    video_results = {}

    for video_name in tqdm(successful_videos, desc="Creating comparison videos"):
        try:
            # Create comparison frames
            frame_result = create_comparison_frames(video_name, inference_dir, test_dir, output_dir)

            if frame_result is None:
                video_results[video_name] = {'status': 'failed', 'reason': 'frame_creation_failed'}
                continue

            # Create video
            video_output_path = os.path.join(output_dir, video_name, f"{video_name}_comparison.mp4")

            if create_video_with_ffmpeg(frame_result['frames_dir'], video_output_path, fps, quality):
                video_results[video_name] = {
                    'status': 'success',
                    'video_path': video_output_path,
                    'num_frames': frame_result['num_frames'],
                    'fps': fps,
                    'quality': quality
                }
                print(f"✓ Created video: {video_output_path}")
            else:
                video_results[video_name] = {'status': 'failed', 'reason': 'video_creation_failed'}

        except Exception as e:
            video_results[video_name] = {'status': 'failed', 'reason': str(e)}
            print(f"✗ Failed to process {video_name}: {str(e)}")

    return video_results


def create_summary_video(video_results, output_dir, max_videos=5):
    """Create a summary video with clips from multiple videos"""
    successful_videos = [(name, result) for name, result in video_results.items()
                        if result['status'] == 'success']

    if len(successful_videos) == 0:
        print("No successful videos to create summary")
        return None

    # Limit number of videos in summary
    selected_videos = successful_videos[:max_videos]

    # Create temporary file list for ffmpeg concat
    concat_file = os.path.join(output_dir, 'concat_list.txt')

    with open(concat_file, 'w') as f:
        for video_name, result in selected_videos:
            video_path = result['video_path']
            # Take first 5 seconds of each video
            f.write(f"file '{os.path.abspath(video_path)}'\n")
            f.write(f"inpoint 0\n")
            f.write(f"outpoint 5\n")

    # Create summary video
    summary_video_path = os.path.join(output_dir, 'summary_comparison.mp4')

    cmd = [
        'ffmpeg',
        '-y',
        '-f', 'concat',
        '-safe', '0',
        '-i', concat_file,
        '-c', 'copy',
        summary_video_path
    ]

    try:
        subprocess.run(cmd, capture_output=True, text=True, check=True)
        os.remove(concat_file)  # Clean up
        return summary_video_path
    except subprocess.CalledProcessError as e:
        print(f"Failed to create summary video: {e.stderr}")
        if os.path.exists(concat_file):
            os.remove(concat_file)
        return None


def main():
    parser = argparse.ArgumentParser(description='Create comparison videos from inference results')
    parser.add_argument('--inference_dir', required=True,
                       help='Directory containing inference results')
    parser.add_argument('--test_dir', default='./dataset/test',
                       help='Path to test dataset directory')
    parser.add_argument('--output_dir', default=None,
                       help='Output directory for videos (default: inference_dir/videos)')
    parser.add_argument('--fps', type=int, default=30,
                       help='Video frame rate (default: 30)')
    parser.add_argument('--quality', choices=['low', 'medium', 'high'], default='medium',
                       help='Video quality (default: medium)')
    parser.add_argument('--create_summary', action='store_true',
                       help='Create a summary video with clips from multiple videos')

    args = parser.parse_args()

    # Check ffmpeg
    if not check_ffmpeg():
        print("Error: ffmpeg not found. Please install ffmpeg to create videos.")
        print("Download from: https://ffmpeg.org/download.html")
        sys.exit(1)

    # Set output directory
    if args.output_dir is None:
        args.output_dir = os.path.join(args.inference_dir, 'videos')

    os.makedirs(args.output_dir, exist_ok=True)

    print(f"Creating comparison videos...")
    print(f"Inference dir: {args.inference_dir}")
    print(f"Test dir: {args.test_dir}")
    print(f"Output dir: {args.output_dir}")
    print(f"FPS: {args.fps}, Quality: {args.quality}")

    # Process all videos
    video_results = process_inference_results(
        args.inference_dir,
        args.test_dir,
        args.output_dir,
        args.fps,
        args.quality
    )

    # Save results
    results_file = os.path.join(args.output_dir, 'video_creation_results.json')
    with open(results_file, 'w') as f:
        json.dump(video_results, f, indent=2)

    # Create summary video if requested
    if args.create_summary:
        print("\nCreating summary video...")
        summary_path = create_summary_video(video_results, args.output_dir)
        if summary_path:
            print(f"✓ Summary video created: {summary_path}")

    # Print summary
    successful = sum(1 for r in video_results.values() if r['status'] == 'success')
    failed = len(video_results) - successful

    print(f"\n=== Summary ===")
    print(f"Total videos processed: {len(video_results)}")
    print(f"Successful: {successful}")
    print(f"Failed: {failed}")
    print(f"Results saved to: {results_file}")


if __name__ == "__main__":
    main()
