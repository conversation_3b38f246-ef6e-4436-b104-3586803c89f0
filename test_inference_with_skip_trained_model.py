#!/usr/bin/env python3
"""
Test inference with models trained using skip augmentation.
This script verifies that models trained with skip augmentation work correctly during inference.
"""

import os
import sys
import torch
import cv2
import numpy as np
from datetime import datetime

# Add current directory to path
sys.path.append('.')

from models.sdc_net2d_with_masks import SDCNet2DWithMasks
from datasets.frame_loader_with_masks import FrameLoaderWithMasks

class InferenceArgs:
    """Arguments for inference (always consecutive sequences)"""
    def __init__(self):
        self.sequence_length = 2
        self.rgb_max = 255.0
        self.flownet2_checkpoint = './flownet2_pytorch/FlowNet2_checkpoint.pth.tar'
        self.fp16 = False
        self.sample_rate = 1
        self.crop_size = [256, 320]
        self.start_index = 0
        self.stride = 64
        # Always False for inference - we use consecutive sequences only
        self.skip_augmentation = False

def load_model_for_inference(checkpoint_path, device):
    """Load a model trained with skip augmentation for inference"""
    
    print(f"🔍 Loading model from: {checkpoint_path}")
    
    if not os.path.exists(checkpoint_path):
        raise FileNotFoundError(f"Checkpoint not found: {checkpoint_path}")
    
    # Load checkpoint
    checkpoint = torch.load(checkpoint_path, map_location=device)
    
    # Create inference args (always consecutive sequences)
    args = InferenceArgs()
    
    # Check training configuration
    if 'args' in checkpoint:
        training_args = checkpoint['args']
        print(f"📊 Training Configuration:")
        print(f"   - Model: {training_args['model']}")
        print(f"   - Skip augmentation: {training_args.get('skip_augmentation', False)}")
        print(f"   - Epochs trained: {checkpoint.get('epoch', 'unknown')}")
        
        if training_args.get('skip_augmentation', False):
            print("✅ Model was trained with skip augmentation")
            print("   Inference will use consecutive sequences (standard behavior)")
        else:
            print("   Model was trained with consecutive sequences only")
    else:
        print("⚠️  No training args found in checkpoint")
    
    # Create model
    model = SDCNet2DWithMasks(args)
    
    # Load state dict
    if 'model_state_dict' in checkpoint:
        state_dict = checkpoint['model_state_dict']
    elif 'state_dict' in checkpoint:
        state_dict = checkpoint['state_dict']
    else:
        state_dict = checkpoint
    
    model.load_state_dict(state_dict, strict=False)
    model.to(device)
    model.eval()
    
    print("✅ Model loaded successfully for inference")
    return model, args

def test_single_prediction(model, test_dir, device, args):
    """Test a single prediction to verify everything works"""
    
    print(f"\n🧪 Testing single prediction...")
    
    # Load test dataset (consecutive sequences only)
    dataset = FrameLoaderWithMasks(args, test_dir, is_training=False)
    
    if len(dataset.ref) == 0:
        print("❌ No videos found in test dataset")
        return False
    
    # Get first video
    video_data = dataset.ref[0]
    video_name = video_data['video_name']
    frame_files = video_data['frames']
    mask_files = video_data['masks']
    
    print(f"   Testing with video: {video_name}")
    print(f"   Available frames: {len(frame_files)}")
    
    if len(frame_files) < args.sequence_length + 1:
        print(f"❌ Not enough frames for prediction (need {args.sequence_length + 1}, have {len(frame_files)})")
        return False
    
    # Load frames and masks for first prediction
    sequence_frames = []
    sequence_masks = []
    
    # Load input frames (t-1, t) and target frame (t+1)
    for j in range(args.sequence_length + 1):
        # Load frame
        frame = cv2.imread(frame_files[j])[..., :3]
        frame = torch.from_numpy(frame.transpose(2, 0, 1)).float()
        sequence_frames.append(frame)
        
        # Load mask
        mask = cv2.imread(mask_files[j], cv2.IMREAD_GRAYSCALE)
        mask = np.where(mask > 127, 255, 0).astype(np.uint8)
        mask = torch.from_numpy(mask).float().unsqueeze(0)
        sequence_masks.append(mask)
    
    # Create input dict
    input_dict = {
        'image': [frame.unsqueeze(0).to(device) for frame in sequence_frames],
        'mask': [mask.unsqueeze(0).to(device) for mask in sequence_masks]
    }
    
    print(f"   Input frames: {len(input_dict['image'])}")
    print(f"   Input masks: {len(input_dict['mask'])}")
    
    # Perform inference
    try:
        with torch.no_grad():
            losses, prediction, target = model(input_dict)
        
        print(f"   ✅ Inference successful!")
        print(f"   Losses:")
        print(f"     - Total: {losses['tot'].item():.4f}")
        print(f"     - Color: {losses['color'].item():.4f}")
        print(f"     - Gradient: {losses['color_gradient'].item():.4f}")
        print(f"     - Smoothness: {losses['flow_smoothness'].item():.4f}")
        
        # Check prediction shape
        pred_shape = prediction.shape
        print(f"   Prediction shape: {pred_shape}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Inference failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def find_skip_trained_checkpoint():
    """Find a checkpoint trained with skip augmentation"""
    
    checkpoint_dir = "./checkpoints"
    if not os.path.exists(checkpoint_dir):
        return None
    
    # Look for checkpoints
    import glob
    checkpoints = glob.glob(os.path.join(checkpoint_dir, "*.pth"))
    
    for ckpt_path in checkpoints:
        try:
            checkpoint = torch.load(ckpt_path, map_location='cpu')
            if 'args' in checkpoint:
                training_args = checkpoint['args']
                if training_args.get('skip_augmentation', False):
                    return ckpt_path
        except:
            continue
    
    return None

def main():
    """Main test function"""
    
    print("🚀 Testing Inference with Skip-Trained Models")
    print("=" * 60)
    
    # Setup device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Device: {device}")
    
    # Find a checkpoint trained with skip augmentation
    checkpoint_path = find_skip_trained_checkpoint()
    
    if checkpoint_path is None:
        print("❌ No checkpoint trained with skip augmentation found")
        print("   Please train a model with --skip_augmentation first")
        return
    
    print(f"Found skip-trained checkpoint: {os.path.basename(checkpoint_path)}")
    
    # Test dataset path
    test_dir = "./dataset/test"
    if not os.path.exists(test_dir):
        print(f"❌ Test dataset not found: {test_dir}")
        return
    
    try:
        # Load model
        model, args = load_model_for_inference(checkpoint_path, device)
        
        # Test single prediction
        success = test_single_prediction(model, test_dir, device, args)
        
        if success:
            print(f"\n🎉 SUCCESS!")
            print(f"✅ Model trained with skip augmentation works correctly for inference")
            print(f"✅ Inference uses consecutive sequences as expected")
            print(f"\nTo run full inference on test set:")
            print(f"python test_inference.py --checkpoint {checkpoint_path}")
        else:
            print(f"\n❌ FAILED!")
            print(f"There was an issue with inference")
            
    except Exception as e:
        print(f"\n❌ ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
