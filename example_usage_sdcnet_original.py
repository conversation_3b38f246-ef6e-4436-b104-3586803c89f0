#!/usr/bin/env python3
"""
Example usage script for SDCNet Original Training System
This script demonstrates how to use the new training system for original SDCNet.
"""

import os
import sys
import argparse

# Add current directory to path
sys.path.append('.')


def show_system_overview():
    """Show overview of the SDCNet Original training system"""
    print("SDCNet Original Training System - Overview")
    print("=" * 60)
    print("""
🎯 PURPOSE:
Train the original SDCNet2D model (without masks) for fair comparison 
with the mask-enhanced version.

🔧 FEATURES:
✅ Standard consecutive frame training (t-1,t → t+1)
✅ Skip frame augmentation (t-2,t → t+2)
✅ GPU support with automatic device detection
✅ Real-time progress monitoring with tqdm
✅ Early stopping with best model restoration
✅ Visual inference monitoring during training
✅ Comprehensive checkpoint management
✅ Resume training capability

📊 COMPARISON WITH MASK VERSION:
┌─────────────────────┬─────────────────────┬─────────────────────┐
│ Aspect              │ Original SDCNet2D   │ SDCNet2DWithMasks   │
├─────────────────────┼─────────────────────┼─────────────────────┤
│ Input Frames        │ 2 (t-1, t)          │ 2 (t-1, t)          │
│ Input Masks         │ 0                   │ 3 (t-1, t, t+1)     │
│ Output              │ 1 frame (t+1)       │ 1 frame (t+1)       │
│ Input Channels      │ 8                   │ 11                  │
│ Channel Breakdown   │ 6 RGB + 2 flow     │ 6 RGB + 3 mask +   │
│                     │                     │ 2 flow              │
│ Dataset Class       │ FrameLoaderOriginal │ FrameLoaderWithMasks│
│ Model Class         │ SDCNet2D            │ SDCNet2DWithMasks   │
└─────────────────────┴─────────────────────┴─────────────────────┘
""")


def show_quick_start_guide():
    """Show quick start guide"""
    print("\n🚀 QUICK START GUIDE")
    print("=" * 60)
    print("""
STEP 1: Test Your Setup
-----------------------
# Run comprehensive tests
python test_sdcnet_original.py --dataset_path ./dataset/train

# Quick training test (2 epochs, minimal batches)
python train_sdcnet_original_quick_test.py \\
  --train_file ./dataset/train \\
  --val_file ./dataset/val \\
  --gpu 0

STEP 2: Standard Training (Consecutive Frames Only)
---------------------------------------------------
python train_sdcnet_original.py \\
  --train_file ./dataset/train \\
  --val_file ./dataset/val \\
  --epochs 100 \\
  --batch_size 4 \\
  --lr 0.0001 \\
  --name sdcnet_original_standard \\
  --gpu 0

STEP 3: Training with Skip Augmentation
---------------------------------------
python train_sdcnet_original.py \\
  --skip_augmentation \\
  --train_file ./dataset/train \\
  --val_file ./dataset/val \\
  --epochs 100 \\
  --batch_size 4 \\
  --lr 0.0001 \\
  --name sdcnet_original_with_skip \\
  --gpu 0

STEP 4: Windows Users - Use Batch Script
-----------------------------------------
# Double-click or run from command prompt
run_training_sdcnet_original.bat
""")


def show_dataset_requirements():
    """Show dataset structure requirements"""
    print("\n📁 DATASET REQUIREMENTS")
    print("=" * 60)
    print("""
Expected dataset structure:
dataset/
├── train/
│   └── X/
│       ├── video_name_1/
│       │   ├── frame_001.png
│       │   ├── frame_002.png
│       │   └── ...
│       ├── video_name_2/
│       │   ├── frame_001.png
│       │   ├── frame_002.png
│       │   └── ...
│       └── ...
└── val/
    └── X/
        ├── video_name_1/
        │   ├── frame_001.png
        │   ├── frame_002.png
        │   └── ...
        └── ...

IMPORTANT NOTES:
- No mask directories needed (unlike mask version)
- Each video must have at least 3 frames for consecutive sequences
- Each video must have at least 5 frames for skip augmentation
- Frame files should be PNG or JPG format
- Frames should be 320x256 pixels (will be automatically padded/cropped)
""")


def show_training_parameters():
    """Show training parameter recommendations"""
    print("\n🎛️ TRAINING PARAMETERS")
    print("=" * 60)
    print("""
RECOMMENDED SETTINGS:
--------------------
--epochs 100              # Sufficient for convergence
--batch_size 4            # Good balance of memory and performance
--lr 0.0001              # Stable learning rate
--patience 10            # Early stopping patience
--save_freq 5            # Save checkpoint every 5 epochs
--workers 4              # Data loading workers

MEMORY REQUIREMENTS:
-------------------
Batch size 4: ~6-8 GB GPU memory
Batch size 2: ~3-4 GB GPU memory (for smaller GPUs)

SKIP AUGMENTATION:
-----------------
--skip_augmentation      # Enable skip frame augmentation
                        # Nearly doubles dataset size
                        # Better performance on fast motion

MONITORING:
----------
--inference_batches 1 1000 2000  # Save visual samples at these batches
--inference_samples 3            # Number of samples to save
""")


def show_expected_results():
    """Show expected training results"""
    print("\n📊 EXPECTED RESULTS")
    print("=" * 60)
    print("""
DATASET SIZE WITH SKIP AUGMENTATION:
-----------------------------------
Without Skip: ~X training sequences
With Skip:    ~2X training sequences (consecutive + skip)

TRAINING TIME:
-------------
Standard training:      ~1-2 hours per epoch
With skip augmentation: ~2-4 hours per epoch

PERFORMANCE COMPARISON:
----------------------
Original SDCNet:     Baseline performance
With Skip Aug:       Better on fast motion scenarios
With Masks:          Best overall performance (expected)

MONITORING OUTPUT:
-----------------
✓ Real-time progress bars with loss values
✓ Epoch statistics with loss breakdown
✓ Visual inference samples saved during training
✓ Automatic best model detection and saving
✓ Early stopping when training plateaus
""")


def show_troubleshooting():
    """Show troubleshooting guide"""
    print("\n🛠️ TROUBLESHOOTING")
    print("=" * 60)
    print("""
COMMON ISSUES AND SOLUTIONS:
----------------------------

1. Dataset Loading Errors:
   python test_sdcnet_original.py --dataset_path ./dataset/train

2. GPU Memory Issues:
   --batch_size 2 --val_batch_size 1

3. FlowNet2 Checkpoint Missing:
   Download from: https://github.com/NVIDIA/flownet2-pytorch
   Place at: ./flownet2_pytorch/FlowNet2_checkpoint.pth.tar

4. Slow Training:
   --workers 2  # Reduce if CPU is bottleneck

5. Disk Space Issues:
   Monitor ./checkpoints/ directory
   Each checkpoint ~1GB

VALIDATION CHECKLIST:
--------------------
□ Dataset properly structured
□ FlowNet2 checkpoint available
□ GPU available and sufficient memory
□ Quick test completed successfully
□ Sufficient disk space for checkpoints
""")


def demonstrate_usage():
    """Demonstrate actual usage with code examples"""
    print("\n💻 CODE EXAMPLES")
    print("=" * 60)
    print("""
BASIC MODEL USAGE:
-----------------
import torch
from models.sdc_net2d import SDCNet2D
from datasets.frame_loader_original import FrameLoaderOriginal

# Create model
args = argparse.Namespace()
args.sequence_length = 2
args.rgb_max = 255.0
args.flownet2_checkpoint = './flownet2_pytorch/FlowNet2_checkpoint.pth.tar'

model = SDCNet2D(args)

# Create dataset
args.sample_rate = 1
args.crop_size = [256, 320]
args.start_index = 0
args.stride = 64
args.skip_augmentation = False

dataset = FrameLoaderOriginal(args, './dataset/train', is_training=True)

# Forward pass
sample = dataset[0]
losses, prediction, target = model(sample)

TRAINING LOOP EXAMPLE:
---------------------
for epoch in range(epochs):
    for batch in train_loader:
        optimizer.zero_grad()
        losses, pred, target = model(batch)
        losses['tot'].backward()
        optimizer.step()
""")


def main():
    """Main function to show all examples"""
    parser = argparse.ArgumentParser(description='SDCNet Original Usage Examples')
    parser.add_argument('--section', type=str, choices=[
        'overview', 'quickstart', 'dataset', 'parameters', 
        'results', 'troubleshooting', 'code', 'all'
    ], default='all', help='Show specific section')
    
    args = parser.parse_args()
    
    if args.section in ['overview', 'all']:
        show_system_overview()
    
    if args.section in ['quickstart', 'all']:
        show_quick_start_guide()
    
    if args.section in ['dataset', 'all']:
        show_dataset_requirements()
    
    if args.section in ['parameters', 'all']:
        show_training_parameters()
    
    if args.section in ['results', 'all']:
        show_expected_results()
    
    if args.section in ['troubleshooting', 'all']:
        show_troubleshooting()
    
    if args.section in ['code', 'all']:
        demonstrate_usage()
    
    print("\n" + "=" * 60)
    print("🎯 NEXT STEPS")
    print("=" * 60)
    print("""
1. Run tests to verify setup:
   python test_sdcnet_original.py

2. Quick test training:
   python train_sdcnet_original_quick_test.py --train_file ./dataset/train --val_file ./dataset/val

3. Start full training:
   python train_sdcnet_original.py --train_file ./dataset/train --val_file ./dataset/val

4. Compare with mask version results

For detailed documentation, see: README_SDCNet_Original_Training.md
""")


if __name__ == "__main__":
    main()
