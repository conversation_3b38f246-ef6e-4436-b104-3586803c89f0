# Test Inference and Video Comparison Guide

Questa guida spiega come eseguire l'inferenza sul test set e creare video di comparazione tra frame originali e predetti.

## 🎯 Panoramica

Il sistema è composto da 3 script principali:

1. **`test_inference.py`** - Esegue inferenza su tutto il test set
2. **`create_comparison_videos.py`** - Crea video di comparazione side-by-side
3. **`run_test_inference.py`** - Pipeline completa che esegue tutto automaticamente

## 📋 Prerequisiti

### Software Richiesto
- **Python 3.7+** con PyTorch e dipendenze del progetto
- **ffmpeg** per la creazione video
  - Windows: Scarica da [ffmpeg.org](https://ffmpeg.org/download.html)
  - Linux: `sudo apt install ffmpeg`
  - macOS: `brew install ffmpeg`

### Struttura Dataset
Il test set deve seguire questa struttura:
```
dataset/test/
├── X/                          # Input frames e masks
│   ├── video_name_1/           # Frame del video
│   ├── video_name_1_binary_ground_truth/  # Mask binarie
│   ├── video_name_2/
│   ├── video_name_2_binary_ground_truth/
│   └── ...
└── Y/                          # Target frames (ground truth)
    ├── video_name_1/
    ├── video_name_2/
    └── ...
```

### Modello Addestrato
Assicurati di avere un checkpoint del modello addestrato in `./checkpoints/`

## 🚀 Uso Rapido

### Opzione 1: Pipeline Completa (Raccomandato)
```bash
# Esegue tutto automaticamente
python run_test_inference.py

# Con parametri personalizzati
python run_test_inference.py \
  --test_dir ./dataset/test \
  --gpu 0 \
  --fps 30 \
  --quality high \
  --create_summary
```

### Opzione 2: Step Separati

#### Step 1: Inferenza
```bash
python test_inference.py \
  --test_dir ./dataset/test \
  --checkpoint ./checkpoints/best_model.pth \
  --output_dir ./test_results \
  --gpu 0
```

#### Step 2: Creazione Video
```bash
python create_comparison_videos.py \
  --inference_dir ./test_results/inference_2024_01_15_14_30_25 \
  --test_dir ./dataset/test \
  --fps 30 \
  --quality medium \
  --create_summary
```

## 📊 Output Generato

### Struttura Directory Risultati
```
test_results/
└── inference_2024_01_15_14_30_25/
    ├── inference_config.json          # Configurazione usata
    ├── inference_results.json         # Risultati per video
    ├── pipeline_summary.json          # Riassunto pipeline
    ├── video_name_1/                  # Risultati per video 1
    │   ├── pred_00003.png             # Frame predetti
    │   ├── pred_00004.png
    │   ├── ...
    │   └── metadata.json              # Metadati e loss medie
    ├── video_name_2/                  # Risultati per video 2
    └── videos/                        # Video di comparazione
        ├── video_creation_results.json
        ├── summary_comparison.mp4      # Video riassuntivo
        ├── video_name_1/
        │   ├── comparison_frames/      # Frame side-by-side
        │   └── video_name_1_comparison.mp4
        └── video_name_2/
            ├── comparison_frames/
            └── video_name_2_comparison.mp4
```

### File di Output Principali

#### 1. Video di Comparazione
- **Individuali**: `videos/{video_name}/{video_name}_comparison.mp4`
- **Riassuntivo**: `videos/summary_comparison.mp4` (primi 5 secondi di ogni video)

#### 2. Metadati JSON
```json
{
  "video_name": "bmx-bumps_1",
  "num_predictions": 87,
  "frame_indices": [2, 3, 4, ...],
  "average_losses": {
    "tot": 0.234,
    "color": 0.156,
    "color_gradient": 0.045,
    "flow_smoothness": 0.033
  }
}
```

## ⚙️ Parametri Configurabili

### test_inference.py
```bash
--test_dir          # Directory test set (default: ./dataset/test)
--checkpoint        # Checkpoint modello (auto-detect se non specificato)
--output_dir        # Directory output (default: ./test_results)
--gpu              # ID GPU (default: 0)
--batch_size       # Batch size (default: 1)
```

### create_comparison_videos.py
```bash
--inference_dir     # Directory risultati inferenza
--test_dir         # Directory test set
--output_dir       # Directory output video (default: inference_dir/videos)
--fps              # Frame rate video (default: 30)
--quality          # Qualità video: low/medium/high (default: medium)
--create_summary   # Crea video riassuntivo
```

### run_test_inference.py
```bash
--test_dir         # Directory test set
--checkpoint       # Checkpoint modello
--output_dir       # Directory base output
--gpu              # ID GPU
--fps              # Frame rate video
--quality          # Qualità video
--create_summary   # Crea video riassuntivo
--skip_inference   # Salta inferenza (usa risultati esistenti)
--skip_videos      # Salta creazione video
```

## 🎬 Qualità Video

### Impostazioni Qualità
- **low**: CRF 28, preset fast (~50% dimensione)
- **medium**: CRF 23, preset medium (bilanciato)
- **high**: CRF 18, preset slow (~150% dimensione, migliore qualità)

### Frame Rate Consigliati
- **24 fps**: Cinematico
- **30 fps**: Standard (raccomandato)
- **60 fps**: Fluido (per movimenti veloci)

## 🔧 Risoluzione Problemi

### Errore: "ffmpeg not found"
```bash
# Verifica installazione
ffmpeg -version

# Se non installato:
# Windows: Scarica da ffmpeg.org e aggiungi al PATH
# Linux: sudo apt install ffmpeg
# macOS: brew install ffmpeg
```

### Errore: "No checkpoint found"
```bash
# Specifica checkpoint manualmente
python run_test_inference.py --checkpoint ./checkpoints/your_model.pth

# O verifica che esistano checkpoint in ./checkpoints/
ls -la ./checkpoints/
```

### Errore: "CUDA out of memory"
```bash
# Usa CPU invece di GPU
python test_inference.py --gpu -1

# O riduci batch size
python test_inference.py --batch_size 1
```

### Video non creati correttamente
```bash
# Verifica che i frame siano stati generati
ls test_results/inference_*/video_name/

# Verifica log di ffmpeg
python create_comparison_videos.py --inference_dir ... --test_dir ... -v
```

## 📈 Monitoraggio Progresso

### Durante Inferenza
```
Processing video 1/25: bmx-bumps_1
Predicting bmx-bumps_1: 100%|████████| 87/87 [00:45<00:00,  1.93it/s]
Saved 87 predictions for bmx-bumps_1
```

### Durante Creazione Video
```
Creating comparison videos: 100%|████████| 25/25 [02:15<00:00,  5.41s/it]
✓ Created video: ./test_results/.../videos/bmx-bumps_1/bmx-bumps_1_comparison.mp4
```

## 📊 Analisi Risultati

### Metriche per Video
Ogni video ha metriche di loss salvate in `metadata.json`:
- **tot**: Loss totale
- **color**: Loss colore
- **color_gradient**: Loss gradiente
- **flow_smoothness**: Loss smoothness flusso ottico

### Confronto Visivo
I video side-by-side permettono di valutare:
- Qualità predizione frame
- Consistenza temporale
- Preservazione dettagli
- Gestione movimento

## 🎯 Esempi d'Uso

### Test Rapido su Singolo Video
```bash
# Modifica test_inference.py per processare solo un video
# Oppure crea subset del test set
```

### Analisi Qualitativa
```bash
# Crea video alta qualità per presentazioni
python run_test_inference.py --quality high --fps 60 --create_summary
```

### Batch Processing
```bash
# Processa più checkpoint
for ckpt in checkpoints/*.pth; do
    python run_test_inference.py --checkpoint "$ckpt" --output_dir "results_$(basename $ckpt)"
done
```

## 📝 Note Importanti

1. **Spazio Disco**: Ogni video può generare centinaia di frame. Assicurati di avere spazio sufficiente.

2. **Tempo Elaborazione**: L'inferenza può richiedere tempo. Per 25 video con ~80 frame ciascuno, stima ~30-60 minuti su GPU.

3. **Memoria GPU**: Il modello carica tutto in memoria. Se hai problemi, usa `--gpu -1` per CPU.

4. **Formato Frame**: I frame devono essere PNG con numerazione sequenziale (00000.png, 00001.png, ...).

5. **Checkpoint**: Lo script cerca automaticamente il checkpoint più recente in `./checkpoints/`. Specifica manualmente se necessario.

## 🆘 Supporto

Se incontri problemi:
1. Verifica i prerequisiti
2. Controlla i log di errore
3. Testa con un singolo video prima
4. Verifica la struttura del dataset
