#!/usr/bin/env python3
"""
Test script for SDCNet2D Original
This script demonstrates how to use the original SDCNet without masks.
"""

import torch
import argparse
import os
import sys

# Add the current directory to Python path
sys.path.append('.')

from models.sdc_net2d import SDCNet2D
from datasets.frame_loader_original import <PERSON><PERSON><PERSON><PERSON>der<PERSON><PERSON><PERSON>


def create_test_args():
    """Create test arguments for the model"""
    args = argparse.Namespace()

    # Model parameters
    args.sequence_length = 2
    args.rgb_max = 255.0
    args.flownet2_checkpoint = './flownet2_pytorch/FlowNet2_checkpoint.pth.tar'

    # FlowNet2 parameters (required)
    args.fp16 = False

    # Dataset parameters
    args.sample_rate = 1
    args.crop_size = [256, 320]  # Height, Width
    args.start_index = 0
    args.stride = 64
    args.skip_augmentation = False  # Test without skip augmentation first

    return args


def test_model_creation():
    """Test if the model can be created successfully"""
    print("Testing SDCNet2D model creation...")

    try:
        args = create_test_args()
        model = SDCNet2D(args)

        # Count parameters
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)

        print(f"✓ Model created successfully!")
        print(f"Total parameters: {total_params:,}")
        print(f"Trainable parameters: {trainable_params:,}")

        return True, model

    except Exception as e:
        print(f"✗ Model creation failed: {e}")
        return False, None


def test_model_forward():
    """Test if the model can perform forward pass"""
    print("\nTesting model forward pass...")

    try:
        args = create_test_args()
        model = SDCNet2D(args)
        model.eval()

        # Create dummy input (batch_size=1, sequence_length+1=3 frames)
        batch_size = 1
        height, width = 256, 320
        num_frames = args.sequence_length + 1  # 3 frames for t-1, t, t+1

        dummy_images = [torch.randn(batch_size, 3, height, width) for _ in range(num_frames)]

        input_dict = {
            'image': dummy_images
        }

        with torch.no_grad():
            losses, prediction, target = model(input_dict)

        print(f"✓ Forward pass successful!")
        print(f"Prediction shape: {prediction.shape}")
        print(f"Target shape: {target.shape}")
        print(f"Loss components: {list(losses.keys())}")
        print(f"Total loss: {losses['tot'].item():.4f}")

        return True

    except Exception as e:
        print(f"✗ Forward pass failed: {e}")
        return False


def test_dataset_loading(dataset_path=None):
    """Test if the dataset can be loaded successfully"""
    print("\nTesting dataset loading...")

    # Use provided path or try to find dataset
    if dataset_path is None:
        test_dataset_path = './dataset/train'
        if not os.path.exists(test_dataset_path):
            print(f"✗ Dataset not found at {test_dataset_path}")
            print("Please provide dataset path or ensure dataset exists")
            return False
    else:
        test_dataset_path = dataset_path

    try:
        args = create_test_args()
        dataset = FrameLoaderOriginal(args, test_dataset_path, is_training=True)
        print(f"✓ Dataset loaded successfully!")
        print(f"Dataset size: {len(dataset)}")

        if len(dataset) > 0:
            # Test loading a sample
            sample = dataset[0]
            print(f"Sample keys: {list(sample.keys())}")
            print(f"Number of images: {len(sample['image'])}")
            print(f"Image shape: {sample['image'][0].shape}")
            print(f"Sequence type: {sample.get('sequence_type', 'N/A')}")

        return True

    except Exception as e:
        print(f"✗ Dataset loading failed: {e}")
        return False


def test_skip_augmentation(dataset_path=None):
    """Test skip augmentation functionality"""
    print("\nTesting skip augmentation...")

    # Use provided path or try to find dataset
    if dataset_path is None:
        test_dataset_path = './dataset/train'
        if not os.path.exists(test_dataset_path):
            print(f"✗ Dataset not found at {test_dataset_path}")
            return False
    else:
        test_dataset_path = dataset_path

    try:
        args = create_test_args()
        args.skip_augmentation = True  # Enable skip augmentation

        dataset = FrameLoaderOriginal(args, test_dataset_path, is_training=True)
        print(f"✓ Skip augmentation dataset loaded successfully!")
        print(f"Dataset size with skip augmentation: {len(dataset)}")

        if hasattr(dataset, 'consecutive_total') and hasattr(dataset, 'skip_total'):
            print(f"Consecutive sequences: {dataset.consecutive_total}")
            print(f"Skip sequences: {dataset.skip_total}")
            print(f"Total sequences: {dataset.total}")

        if len(dataset) > 0:
            # Test loading samples
            sample1 = dataset[0]  # Should be consecutive
            print(f"Sample 1 sequence type: {sample1.get('sequence_type', 'N/A')}")

            if len(dataset) > dataset.consecutive_total:
                sample2 = dataset[dataset.consecutive_total]  # Should be skip
                print(f"Sample 2 sequence type: {sample2.get('sequence_type', 'N/A')}")

        return True

    except Exception as e:
        print(f"✗ Skip augmentation test failed: {e}")
        return False


def test_training_compatibility():
    """Test if model and dataset are compatible for training"""
    print("\nTesting training compatibility...")

    try:
        args = create_test_args()
        model = SDCNet2D(args)

        # Create dummy dataset sample
        batch_size = 2
        height, width = 256, 320
        num_frames = args.sequence_length + 1

        dummy_images = [torch.randn(batch_size, 3, height, width) for _ in range(num_frames)]

        input_dict = {
            'image': dummy_images
        }

        # Test forward pass
        model.train()
        losses, prediction, target = model(input_dict)

        # Test backward pass
        total_loss = losses['tot']
        total_loss.backward()

        print(f"✓ Training compatibility test successful!")
        print(f"Batch size: {batch_size}")
        print(f"Loss: {total_loss.item():.4f}")

        return True

    except Exception as e:
        print(f"✗ Training compatibility test failed: {e}")
        return False


def main():
    """Run all tests"""
    parser = argparse.ArgumentParser(description='Test SDCNet2D Original')
    parser.add_argument('--dataset_path', type=str, default=None,
                       help='Path to dataset for testing (optional)')
    args = parser.parse_args()

    print("SDCNet2D Original - Test Suite")
    print("=" * 60)

    # Run tests
    tests_passed = 0
    total_tests = 5

    # Test 1: Model creation
    if test_model_creation()[0]:
        tests_passed += 1

    # Test 2: Model forward pass
    if test_model_forward():
        tests_passed += 1

    # Test 3: Dataset loading
    if test_dataset_loading(args.dataset_path):
        tests_passed += 1

    # Test 4: Skip augmentation
    if test_skip_augmentation(args.dataset_path):
        tests_passed += 1

    # Test 5: Training compatibility
    if test_training_compatibility():
        tests_passed += 1

    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"Tests passed: {tests_passed}/{total_tests}")

    if tests_passed == total_tests:
        print("🎉 ALL TESTS PASSED!")
        print("\nYou can now proceed with training:")
        print("1. Quick test: python train_sdcnet_original_quick_test.py --train_file ./dataset/train --val_file ./dataset/val")
        print("2. Full training: python train_sdcnet_original.py --train_file ./dataset/train --val_file ./dataset/val")
        print("3. With skip augmentation: python train_sdcnet_original.py --skip_augmentation --train_file ./dataset/train --val_file ./dataset/val")
    else:
        print("❌ SOME TESTS FAILED!")
        print("\nPlease fix the issues before proceeding with training.")

    print("\n" + "=" * 60)


if __name__ == "__main__":
    main()
