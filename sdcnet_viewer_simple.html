<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SDCNet Results Comparison Viewer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1a1a1a;
            color: #ffffff;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #4CAF50;
            margin-bottom: 10px;
        }

        .controls {
            background: #2d2d2d;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            align-items: center;
            justify-content: center;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
        }

        .control-group label {
            font-size: 12px;
            color: #cccccc;
            text-transform: uppercase;
        }

        select, input, button {
            padding: 8px 12px;
            border: none;
            border-radius: 5px;
            background: #404040;
            color: #ffffff;
            font-size: 14px;
        }

        select:focus, input:focus {
            outline: 2px solid #4CAF50;
        }

        button {
            background: #4CAF50;
            cursor: pointer;
            transition: background 0.3s;
        }

        button:hover {
            background: #45a049;
        }

        button:disabled {
            background: #666666;
            cursor: not-allowed;
        }

        .frame-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .grid-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .viewer-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .display-container {
            background: #2d2d2d;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }

        .display-title {
            color: #4CAF50;
            margin-bottom: 10px;
            font-weight: bold;
            font-size: 16px;
        }

        .display-controls {
            margin-bottom: 15px;
        }

        .source-select {
            width: 100%;
            max-width: 300px;
            margin: 0 auto;
            display: block;
        }

        .image-container {
            position: relative;
            background: #000000;
            border-radius: 5px;
            overflow: hidden;
            display: inline-block;
            max-width: 100%;
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .frame-image {
            max-width: 100%;
            height: auto;
            display: block;
        }

        .grid-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            opacity: 0.7;
        }

        .status {
            text-align: center;
            padding: 20px;
            color: #cccccc;
            font-style: italic;
        }

        .error {
            color: #ff6b6b;
        }

        .frame-info {
            margin-top: 10px;
            font-size: 12px;
            color: #888888;
        }

        .placeholder {
            color: #666666;
            font-style: italic;
        }

        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                gap: 15px;
            }

            .viewer-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎬 SDCNet Results Comparison Viewer</h1>
        <p>Visual comparison of SDCNet variants with flexible display configuration</p>
    </div>

    <div class="controls">
        <div class="control-group">
            <label>Video Selection</label>
            <select id="videoSelect">
                <option value="">Select a video...</option>
            </select>
        </div>

        <div class="frame-controls">
            <button id="prevFrame" disabled>◀ Previous</button>
            <div class="control-group">
                <label>Frame</label>
                <input type="number" id="frameInput" min="0" value="0" disabled>
            </div>
            <button id="nextFrame" disabled>Next ▶</button>
        </div>

        <div class="grid-controls">
            <div class="control-group">
                <label>Grid Size</label>
                <input type="range" id="gridSize" min="1" max="10" value="1">
                <span id="gridSizeLabel">1x1</span>
            </div>
            <div class="control-group">
                <label>Grid Color</label>
                <input type="color" id="gridColor" value="#ff0000">
            </div>
        </div>
    </div>

    <div class="viewer-container">
        <div class="display-container">
            <div class="display-title">Display 1</div>
            <div class="display-controls">
                <select id="display1Select" class="source-select">
                    <option value="">Select source...</option>
                </select>
            </div>
            <div class="image-container" id="display1Container">
                <img id="display1Image" class="frame-image" style="display: none;">
                <canvas class="grid-overlay" id="display1Grid"></canvas>
                <div class="placeholder" id="display1Placeholder">Select a video and source</div>
            </div>
            <div class="frame-info" id="display1Info"></div>
        </div>

        <div class="display-container">
            <div class="display-title">Display 2</div>
            <div class="display-controls">
                <select id="display2Select" class="source-select">
                    <option value="">Select source...</option>
                </select>
            </div>
            <div class="image-container" id="display2Container">
                <img id="display2Image" class="frame-image" style="display: none;">
                <canvas class="grid-overlay" id="display2Grid"></canvas>
                <div class="placeholder" id="display2Placeholder">Select a video and source</div>
            </div>
            <div class="frame-info" id="display2Info"></div>
        </div>

        <div class="display-container">
            <div class="display-title">Display 3</div>
            <div class="display-controls">
                <select id="display3Select" class="source-select">
                    <option value="">Select source...</option>
                </select>
            </div>
            <div class="image-container" id="display3Container">
                <img id="display3Image" class="frame-image" style="display: none;">
                <canvas class="grid-overlay" id="display3Grid"></canvas>
                <div class="placeholder" id="display3Placeholder">Select a video and source</div>
            </div>
            <div class="frame-info" id="display3Info"></div>
        </div>

        <div class="display-container">
            <div class="display-title">Display 4</div>
            <div class="display-controls">
                <select id="display4Select" class="source-select">
                    <option value="">Select source...</option>
                </select>
            </div>
            <div class="image-container" id="display4Container">
                <img id="display4Image" class="frame-image" style="display: none;">
                <canvas class="grid-overlay" id="display4Grid"></canvas>
                <div class="placeholder" id="display4Placeholder">Select a video and source</div>
            </div>
            <div class="frame-info" id="display4Info"></div>
        </div>
    </div>

    <div class="status" id="status">
        Please select a video to begin comparison.
    </div>

    <script>
        // Application state
        let currentVideo = '';
        let currentFrame = 0;
        let maxFrames = 0;
        let availableFrames = [];
        let frameFormat = 5; // Default to 5 digits
        let availableSources = [];

        // DOM elements
        const videoSelect = document.getElementById('videoSelect');
        const frameInput = document.getElementById('frameInput');
        const prevButton = document.getElementById('prevFrame');
        const nextButton = document.getElementById('nextFrame');
        const gridSizeSlider = document.getElementById('gridSize');
        const gridSizeLabel = document.getElementById('gridSizeLabel');
        const gridColorPicker = document.getElementById('gridColor');
        const status = document.getElementById('status');

        // Display elements (4 displays)
        const displays = {};
        for (let i = 1; i <= 4; i++) {
            displays[`display${i}`] = {
                select: document.getElementById(`display${i}Select`),
                image: document.getElementById(`display${i}Image`),
                grid: document.getElementById(`display${i}Grid`),
                info: document.getElementById(`display${i}Info`),
                placeholder: document.getElementById(`display${i}Placeholder`)
            };
        }

        // Initialize the application
        async function init() {
            await loadVideoList();
            setupEventListeners();
            updateGridSizeLabel();
        }

        // Load available videos from the test dataset
        async function loadVideoList() {
            try {
                const response = await fetch('/api/videos');
                const data = await response.json();

                if (data.error) {
                    throw new Error(data.message);
                }

                videoSelect.innerHTML = '<option value="">Select a video...</option>';
                data.videos.forEach(video => {
                    const option = document.createElement('option');
                    option.value = video;
                    option.textContent = video;
                    videoSelect.appendChild(option);
                });
            } catch (error) {
                console.error('Error loading video list:', error);
                status.textContent = 'Error loading video list. Make sure the server is running.';
                status.className = 'status error';
            }
        }

        // Setup event listeners
        function setupEventListeners() {
            videoSelect.addEventListener('change', onVideoChange);
            frameInput.addEventListener('change', onFrameChange);
            prevButton.addEventListener('click', () => changeFrame(-1));
            nextButton.addEventListener('click', () => changeFrame(1));
            gridSizeSlider.addEventListener('input', onGridSizeChange);
            gridColorPicker.addEventListener('change', updateGrids);

            // Setup display source selectors
            for (let i = 1; i <= 4; i++) {
                displays[`display${i}`].select.addEventListener('change', () => onSourceChange(i));
            }

            // Keyboard navigation
            document.addEventListener('keydown', (e) => {
                if (e.key === 'ArrowLeft') {
                    e.preventDefault();
                    changeFrame(-1);
                } else if (e.key === 'ArrowRight') {
                    e.preventDefault();
                    changeFrame(1);
                }
            });
        }

        // Handle video selection change
        async function onVideoChange() {
            currentVideo = videoSelect.value;
            if (currentVideo) {
                await loadVideoData();
                await loadSources();
            } else {
                resetDisplays();
            }
        }

        // Load video data and available frames
        async function loadVideoData() {
            try {
                status.textContent = 'Loading video data...';
                status.className = 'status';

                // Reset frame counter
                currentFrame = 0;
                availableFrames = [];

                // Query server for available frames
                const response = await fetch(`/api/frames?video=${encodeURIComponent(currentVideo)}&inference=dummy`);
                const data = await response.json();

                if (data.error) {
                    throw new Error(data.message);
                }

                availableFrames = data.frames;
                maxFrames = availableFrames.length;
                frameFormat = data.frame_format || 5;

                if (maxFrames > 0) {
                    frameInput.max = maxFrames - 1;
                    frameInput.disabled = false;
                    prevButton.disabled = false;
                    nextButton.disabled = false;

                    frameInput.value = 0;
                    await loadFrame(0);
                    status.textContent = `Loaded ${maxFrames} frames for ${currentVideo}`;
                } else {
                    status.textContent = 'No frames found for this video';
                    status.className = 'status error';
                }
            } catch (error) {
                console.error('Error loading video data:', error);
                status.textContent = 'Error loading video data.';
                status.className = 'status error';
            }
        }

        // Load available sources for the current video
        async function loadSources() {
            try {
                const response = await fetch(`/api/sources?video=${encodeURIComponent(currentVideo)}`);
                const data = await response.json();

                if (data.error) {
                    throw new Error(data.message);
                }

                availableSources = data.sources;

                // Update all display selectors
                for (let i = 1; i <= 4; i++) {
                    const select = displays[`display${i}`].select;
                    select.innerHTML = '<option value="">Select source...</option>';

                    availableSources.forEach(source => {
                        const option = document.createElement('option');
                        option.value = source.id;
                        option.textContent = source.name;
                        option.title = source.description;
                        select.appendChild(option);
                    });
                }

            } catch (error) {
                console.error('Error loading sources:', error);
                status.textContent = 'Error loading sources.';
                status.className = 'status error';
            }
        }

        // Handle source selection change for a display
        async function onSourceChange(displayNum) {
            const display = displays[`display${displayNum}`];
            const sourceId = display.select.value;

            if (sourceId && currentVideo && availableFrames.length > 0) {
                await loadDisplayFrame(displayNum, currentFrame);
            } else {
                // Clear display
                display.image.style.display = 'none';
                display.placeholder.style.display = 'block';
                display.info.textContent = '';
            }
        }

        // Handle frame input change
        function onFrameChange() {
            const newFrame = parseInt(frameInput.value);
            if (newFrame >= 0 && newFrame < maxFrames) {
                loadFrame(newFrame);
            }
        }

        // Change frame by delta
        function changeFrame(delta) {
            const newFrame = currentFrame + delta;
            if (newFrame >= 0 && newFrame < maxFrames) {
                frameInput.value = newFrame;
                loadFrame(newFrame);
            }
        }

        // Load and display a specific frame for all displays
        async function loadFrame(frameIndex) {
            if (frameIndex < 0 || frameIndex >= maxFrames) return;

            currentFrame = frameIndex;

            // Update frame input
            frameInput.value = frameIndex;

            // Update button states
            prevButton.disabled = frameIndex === 0;
            nextButton.disabled = frameIndex === maxFrames - 1;

            // Load frame for all displays that have a source selected
            for (let i = 1; i <= 4; i++) {
                const display = displays[`display${i}`];
                if (display.select.value) {
                    await loadDisplayFrame(i, frameIndex);
                }
            }

            // Update grids
            updateGrids();
        }

        // Load frame for a specific display
        async function loadDisplayFrame(displayNum, frameIndex) {
            const display = displays[`display${displayNum}`];
            const sourceId = display.select.value;

            if (!sourceId || frameIndex < 0 || frameIndex >= maxFrames) return;

            const source = availableSources.find(s => s.id === sourceId);
            if (!source) return;

            const actualFrameNumber = availableFrames[frameIndex];
            const frameStr = actualFrameNumber.toString().padStart(frameFormat, '0');
            const imagePath = source.path_template.replace('{frame}', frameStr);

            try {
                const img = display.image;
                const placeholder = display.placeholder;
                const info = display.info;

                // Show loading state
                img.style.display = 'none';
                placeholder.textContent = 'Loading...';
                placeholder.style.display = 'block';

                await new Promise((resolve, reject) => {
                    img.onload = () => {
                        img.style.display = 'block';
                        placeholder.style.display = 'none';

                        // Resize canvas to match image
                        const canvas = display.grid;
                        canvas.width = img.naturalWidth;
                        canvas.height = img.naturalHeight;
                        canvas.style.width = img.offsetWidth + 'px';
                        canvas.style.height = img.offsetHeight + 'px';

                        info.textContent = `Frame ${frameIndex + 1}/${maxFrames} (${actualFrameNumber}) - ${source.name}`;
                        resolve();
                    };

                    img.onerror = () => {
                        img.style.display = 'none';
                        placeholder.textContent = 'Image not found';
                        placeholder.style.display = 'block';
                        info.textContent = `Frame ${frameIndex + 1}/${maxFrames} (${actualFrameNumber}) - Not found`;
                        resolve(); // Don't reject, just show error state
                    };

                    img.src = imagePath;
                });

            } catch (error) {
                console.error(`Error loading frame for display ${displayNum}:`, error);
                display.placeholder.textContent = 'Error loading image';
                display.placeholder.style.display = 'block';
                display.image.style.display = 'none';
            }
        }

        // Reset all displays
        function resetDisplays() {
            for (let i = 1; i <= 4; i++) {
                const display = displays[`display${i}`];
                display.select.innerHTML = '<option value="">Select source...</option>';
                display.image.style.display = 'none';
                display.placeholder.textContent = 'Select a video and source';
                display.placeholder.style.display = 'block';
                display.info.textContent = '';
            }

            frameInput.disabled = true;
            prevButton.disabled = true;
            nextButton.disabled = true;
            frameInput.value = 0;

            status.textContent = 'Please select a video to begin comparison.';
            status.className = 'status';
        }

        // Handle grid size change
        function onGridSizeChange() {
            updateGridSizeLabel();
            updateGrids();
        }

        // Update grid size label
        function updateGridSizeLabel() {
            const size = gridSizeSlider.value;
            gridSizeLabel.textContent = `${size}x${size}`;
        }

        // Update all grid overlays
        function updateGrids() {
            const gridSize = parseInt(gridSizeSlider.value);
            const gridColor = gridColorPicker.value;

            for (let i = 1; i <= 4; i++) {
                drawGrid(displays[`display${i}`].grid, gridSize, gridColor);
            }
        }

        // Draw grid on canvas
        function drawGrid(canvas, gridSize, color) {
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            if (gridSize <= 1) return;

            ctx.strokeStyle = color;
            ctx.lineWidth = 2;

            const cellWidth = canvas.width / gridSize;
            const cellHeight = canvas.height / gridSize;

            // Draw vertical lines
            for (let i = 1; i < gridSize; i++) {
                const x = i * cellWidth;
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, canvas.height);
                ctx.stroke();
            }

            // Draw horizontal lines
            for (let i = 1; i < gridSize; i++) {
                const y = i * cellHeight;
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(canvas.width, y);
                ctx.stroke();
            }
        }

        // Initialize the application when the page loads
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
