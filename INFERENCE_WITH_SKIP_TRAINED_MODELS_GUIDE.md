# Inference with Skip-Trained Models Guide

## 🎯 Overview

This guide explains how to perform inference with models trained using skip augmentation. The key point is that **inference always uses consecutive sequences**, regardless of how the model was trained.

## 🔧 How It Works

### Training vs Inference
- **Training with Skip Augmentation**: Uses both consecutive (t-1,t→t+1) and skip (t-2,t→t+2) sequences
- **Inference**: Always uses consecutive sequences (t-1,t→t+1) for consistency and compatibility

### Automatic Detection
The inference system automatically detects if a model was trained with skip augmentation and handles it appropriately.

## 🚀 Quick Start

### 1. Automatic Inference (Recommended)
```bash
# Automatically finds the best checkpoint and runs inference
python run_inference_auto.py
```

### 2. Manual Checkpoint Selection
```bash
# List available checkpoints
python run_inference_auto.py --list_checkpoints

# Use specific checkpoint
python test_inference.py --checkpoint ./checkpoints/your_model.pth
```

### 3. Test Skip-Trained Model
```bash
# Quick test to verify skip-trained models work
python test_inference_with_skip_trained_model.py
```

## 📊 Expected Output

When running inference with a skip-trained model, you'll see:

```
📊 Checkpoint Information:
   File: sdcnet_with_masks_best_epoch_030.pth
   Epoch: 30
   Model: SDCNet2DWithMasks
   Skip Augmentation: ✅ Yes
   📝 Note: Model was trained with skip augmentation
           Inference will use consecutive sequences (standard)
```

## 🔍 Available Scripts

### Core Inference Scripts

1. **`run_inference_auto.py`** - Automatic inference (recommended)
   - Auto-detects best checkpoint
   - Shows training configuration
   - Handles both regular and skip-trained models

2. **`test_inference.py`** - Manual inference
   - Specify checkpoint manually
   - Full control over parameters
   - Detailed output

3. **`test_inference_with_skip_trained_model.py`** - Testing script
   - Verifies skip-trained models work correctly
   - Quick validation before full inference

### Utility Scripts

4. **`quick_training_test.py`** - Training validation
5. **`test_skip_augmentation.py`** - Dataset testing
6. **`create_comparison_videos.py`** - Video creation from results

## 📈 Performance Comparison

### Models Trained with Skip Augmentation
- **Better temporal understanding**: Learned from both consecutive and skip sequences
- **Improved fast motion handling**: Enhanced performance on rapid movements
- **Same inference speed**: No performance penalty during inference
- **Backward compatible**: Works with all existing inference scripts

### Expected Benefits
- ✅ Better prediction quality on fast-moving objects
- ✅ More robust temporal consistency
- ✅ Improved handling of challenging motion scenarios
- ✅ Same inference performance as regular models

## 🛠️ Technical Details

### Checkpoint Information
Skip-trained models store training configuration in the checkpoint:
```python
checkpoint = {
    'model_state_dict': ...,
    'args': {
        'model': 'SDCNet2DWithMasks',
        'skip_augmentation': True,  # Training used skip augmentation
        'sequence_length': 2,
        ...
    },
    'epoch': 30,
    ...
}
```

### Inference Configuration
During inference, the dataset loader always uses:
```python
args.skip_augmentation = False  # Always consecutive sequences
```

### Model Compatibility
- ✅ Skip-trained models work with consecutive sequences
- ✅ Regular models work with consecutive sequences
- ✅ No model architecture changes needed
- ✅ Same input/output format

## 📁 Output Structure

Inference results are saved in timestamped directories:
```
test_results/
└── inference_2025_05_29_11_10_08/
    ├── inference_config.json          # Configuration used
    ├── inference_results.json         # Overall results
    ├── bmx-bumps_1/                   # Per-video results
    │   ├── pred_00002.png             # Predicted frames
    │   ├── pred_00003.png
    │   └── metadata.json              # Video metadata
    ├── bmx-bumps_2/
    └── ...
```

## 🎬 Creating Comparison Videos

After inference, create comparison videos:
```bash
python create_comparison_videos.py --results_dir ./test_results/inference_2025_05_29_11_10_08
```

## 🔧 Command Examples

### Basic Usage
```bash
# Simplest approach - auto-detect everything
python run_inference_auto.py

# Specify test directory
python run_inference_auto.py --test_dir ./dataset/test

# Specify output directory
python run_inference_auto.py --output_dir ./my_results
```

### Advanced Usage
```bash
# Use specific checkpoint
python test_inference.py \
    --checkpoint ./checkpoints/sdcnet_with_masks_best_epoch_030.pth \
    --test_dir ./dataset/test \
    --output_dir ./test_results

# Different GPU
python test_inference.py --gpu 1
```

### Validation
```bash
# Test that skip-trained models work
python test_inference_with_skip_trained_model.py

# List all available checkpoints
python run_inference_auto.py --list_checkpoints
```

## ⚠️ Important Notes

### Compatibility
- **Always works**: Skip-trained models are fully compatible with inference
- **No changes needed**: Existing inference scripts work unchanged
- **Consistent behavior**: Inference always uses consecutive sequences

### Performance
- **Same speed**: No performance difference during inference
- **Better quality**: Skip-trained models often produce better results
- **Memory usage**: Same memory requirements as regular models

### Troubleshooting
- **Checkpoint not found**: Use `--list_checkpoints` to see available models
- **CUDA errors**: Reduce batch size or use `--gpu 0`
- **Dataset errors**: Verify test dataset structure with validation scripts

## 🎉 Success Indicators

When everything works correctly, you should see:
- ✅ Automatic checkpoint detection
- ✅ Skip augmentation status displayed
- ✅ All videos processed successfully
- ✅ Results saved to timestamped directory
- ✅ Suggestion for creating comparison videos

## 📚 Related Documentation

- `SKIP_AUGMENTATION_GUIDE.md` - Complete skip augmentation guide
- `SKIP_AUGMENTATION_IMPLEMENTATION_SUMMARY.md` - Implementation details
- `TEST_INFERENCE_README.md` - Original inference documentation
- `TRAINING_SCRIPTS_SUMMARY.md` - Training with skip augmentation

---

**Status**: ✅ **FULLY IMPLEMENTED AND TESTED**

The inference system now seamlessly handles both regular and skip-augmentation trained models, providing better performance while maintaining full backward compatibility.
