@echo off
echo ========================================
echo SDCNet Original Training Script
echo ========================================
echo.
echo This script will train the original SDCNet2D model (without masks)
echo for comparison with the mask-enhanced version.
echo.
echo Training configurations:
echo 1. Standard training (consecutive frames only)
echo 2. Training with skip augmentation (consecutive + skip frames)
echo.

:MENU
echo Please choose training type:
echo [1] Standard Training (t-1,t -^> t+1)
echo [2] Training with Skip Augmentation (t-1,t -^> t+1 + t-2,t -^> t+2)
echo [3] Quick Test (verify setup works)
echo [Q] Quit
echo.
set /p choice="Enter your choice (1/2/3/Q): "

if /i "%choice%"=="1" goto STANDARD
if /i "%choice%"=="2" goto SKIP
if /i "%choice%"=="3" goto QUICKTEST
if /i "%choice%"=="Q" goto END
if /i "%choice%"=="q" goto END

echo Invalid choice. Please try again.
echo.
goto MENU

:STANDARD
echo.
echo ========================================
echo Starting Standard SDCNet Original Training
echo ========================================
echo.
echo Configuration:
echo - Model: SDCNet2D (original, no masks)
echo - Dataset: FrameLoaderOriginal
echo - Sequence: t-1,t -^> t+1 (consecutive frames only)
echo - Epochs: 100
echo - Batch size: 4
echo - Learning rate: 0.0001
echo.

python train_sdcnet_original.py ^
    --model SDCNet2D ^
    --dataset FrameLoaderOriginal ^
    --train_file .\dataset\train\ ^
    --val_file .\dataset\val\ ^
    --epochs 100 ^
    --batch_size 4 ^
    --val_batch_size 2 ^
    --lr 0.0001 ^
    --weight_decay 1e-4 ^
    --workers 4 ^
    --crop_size 256 320 ^
    --sample_rate 1 ^
    --stride 64 ^
    --save_dir .\checkpoints ^
    --name sdcnet_original_standard ^
    --patience 10 ^
    --save_freq 5 ^
    --inference_batches 1 1000 2000 ^
    --inference_samples 3 ^
    --gpu 0

goto COMPLETE

:SKIP
echo.
echo ========================================
echo Starting SDCNet Original Training with Skip Augmentation
echo ========================================
echo.
echo Configuration:
echo - Model: SDCNet2D (original, no masks)
echo - Dataset: FrameLoaderOriginal with skip augmentation
echo - Sequences: t-1,t -^> t+1 + t-2,t -^> t+2 (nearly double dataset size)
echo - Epochs: 100
echo - Batch size: 4
echo - Learning rate: 0.0001
echo.

python train_sdcnet_original.py ^
    --skip_augmentation ^
    --model SDCNet2D ^
    --dataset FrameLoaderOriginal ^
    --train_file .\dataset\train\ ^
    --val_file .\dataset\val\ ^
    --epochs 100 ^
    --batch_size 4 ^
    --val_batch_size 2 ^
    --lr 0.0001 ^
    --weight_decay 1e-4 ^
    --workers 4 ^
    --crop_size 256 320 ^
    --sample_rate 1 ^
    --stride 64 ^
    --save_dir .\checkpoints ^
    --name sdcnet_original_with_skip ^
    --patience 10 ^
    --save_freq 5 ^
    --inference_batches 1 1000 2000 ^
    --inference_samples 3 ^
    --gpu 0

goto COMPLETE

:QUICKTEST
echo.
echo ========================================
echo Starting Quick Test
echo ========================================
echo.
echo This will run a minimal training session to verify everything works.
echo Configuration:
echo - 2 epochs only
echo - Small batch sizes
echo - Limited batches per epoch
echo.

python train_sdcnet_original_quick_test.py ^
    --train_file .\dataset\train\ ^
    --val_file .\dataset\val\ ^
    --gpu 0

echo.
if %ERRORLEVEL% EQU 0 (
    echo ✅ Quick test completed successfully!
    echo You can now run full training with confidence.
    echo.
    echo To run full training:
    echo - Standard: Choose option 1 from the menu
    echo - With skip augmentation: Choose option 2 from the menu
) else (
    echo ❌ Quick test failed. Please check your dataset and configuration.
)
echo.
pause
goto MENU

:COMPLETE
echo.
if %ERRORLEVEL% EQU 0 (
    echo ========================================
    echo 🎉 Training completed successfully!
    echo ========================================
    echo.
    echo Results saved to: .\checkpoints\
    echo.
    echo Next steps:
    echo 1. Check training logs and loss curves
    echo 2. Evaluate model performance on test data
    echo 3. Compare with SDCNet with masks results
    echo.
) else (
    echo ========================================
    echo ❌ Training failed!
    echo ========================================
    echo.
    echo Please check:
    echo 1. Dataset path and structure
    echo 2. GPU availability and memory
    echo 3. Dependencies installation
    echo 4. FlowNet2 checkpoint availability
    echo.
)

:END
echo.
echo Press any key to exit...
pause >nul
