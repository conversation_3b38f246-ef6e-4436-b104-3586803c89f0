#!/usr/bin/env python3
"""
Quick test to verify that training with skip augmentation works correctly.
This script runs a few training steps to ensure everything is working.
"""

import os
import sys
import torch
from torch.utils.data import DataLoader

# Add current directory to path
sys.path.append('.')

from models.sdc_net2d_with_masks import SDCNet2DWithMasks
from datasets.frame_loader_with_masks import FrameLoaderWithMasks

class Args:
    """Simple args class for testing"""
    def __init__(self):
        self.sequence_length = 2
        self.sample_rate = 1
        self.crop_size = [256, 320]
        self.start_index = 0
        self.stride = 64
        self.skip_augmentation = True
        self.rgb_max = 255.0
        self.flownet2_checkpoint = './flownet2_pytorch/FlowNet2_checkpoint.pth.tar'
        self.fp16 = False

def test_training_step():
    """Test a few training steps with skip augmentation"""

    print("🧪 Quick Training Test with Skip Augmentation")
    print("=" * 50)

    # Check if FlowNet2 checkpoint exists
    args = Args()
    if not os.path.exists(args.flownet2_checkpoint):
        print(f"❌ FlowNet2 checkpoint not found: {args.flownet2_checkpoint}")
        print("Please download the FlowNet2 checkpoint first.")
        return False

    # Check if dataset exists
    train_path = './dataset/train/'
    if not os.path.exists(train_path):
        print(f"❌ Training dataset not found: {train_path}")
        return False

    try:
        # Create dataset
        print("Creating dataset...")
        dataset = FrameLoaderWithMasks(args, train_path, is_training=True)

        # Create a small subset for testing
        subset_size = min(10, len(dataset))
        subset_indices = list(range(subset_size))
        subset_dataset = torch.utils.data.Subset(dataset, subset_indices)

        # Create data loader
        loader = DataLoader(subset_dataset, batch_size=2, shuffle=False, num_workers=0)

        print(f"Dataset subset size: {subset_size}")
        print(f"Number of batches: {len(loader)}")

        # Create model
        print("Creating model...")
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model = SDCNet2DWithMasks(args).to(device)

        print(f"Model created on device: {device}")

        # Test a few forward passes
        print("Testing forward passes...")
        model.train()

        for batch_idx, batch in enumerate(loader):
            if batch_idx >= 3:  # Test only first 3 batches
                break

            print(f"\nBatch {batch_idx}:")

            # Move to device
            inputs = {}
            for key in ['image', 'mask']:
                if key in batch:
                    inputs[key] = [tensor.to(device) for tensor in batch[key]]

            # Get batch info
            sequence_types = batch.get('sequence_type', ['unknown'])
            video_names = batch.get('video_name', ['unknown'])

            print(f"  - Sequence types: {sequence_types}")
            print(f"  - Video names: {video_names}")
            print(f"  - Input frames: {len(inputs['image'])}")
            print(f"  - Input masks: {len(inputs['mask'])}")

            # Forward pass
            try:
                losses, prediction, target = model(inputs)

                print(f"  - Total loss: {losses['tot'].item():.4f}")
                print(f"  - Color loss: {losses['color'].item():.4f}")
                print(f"  - Gradient loss: {losses['color_gradient'].item():.4f}")
                print(f"  - Smoothness loss: {losses['flow_smoothness'].item():.4f}")
                print(f"  ✅ Forward pass successful")

            except Exception as e:
                print(f"  ❌ Forward pass failed: {e}")
                return False

        print(f"\n🎉 All tests passed! Skip augmentation is working correctly.")
        return True

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    success = test_training_step()

    if success:
        print(f"\n✅ Quick training test completed successfully!")
        print(f"\nYou can now run full training with:")
        print(f"python train_sdcnet_with_masks.py --skip_augmentation --train_file ./dataset/train/ --val_file ./dataset/val/ ...")
    else:
        print(f"\n❌ Quick training test failed. Please check the setup.")

if __name__ == "__main__":
    main()
