#!/usr/bin/env python3
"""
Create comparison videos from Original SDCNet inference results.
This script creates side-by-side comparison videos showing input, prediction, and target frames.
"""

import os
import sys
import argparse
import cv2
import numpy as np
import json
import glob
import subprocess
from datetime import datetime
from tqdm import tqdm

# Add current directory to path
sys.path.append('.')


def check_ffmpeg():
    """Check if ffmpeg is available"""
    try:
        subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False


def get_original_frames(test_dir, video_name, start_frame=2):
    """Get original frames from test dataset, starting from specified frame"""
    # Look for frames in Y directory (ground truth) first, then X
    for subdir in ['Y', 'X']:
        video_dir = os.path.join(test_dir, subdir, video_name)
        if os.path.exists(video_dir):
            # Get all frame files
            frame_files = []
            for ext in ['*.png', '*.jpg', '*.jpeg']:
                frame_files.extend(glob.glob(os.path.join(video_dir, ext)))

            if frame_files:
                # Sort frames by number
                def get_frame_num(path):
                    filename = os.path.basename(path)
                    name_without_ext = os.path.splitext(filename)[0]
                    import re
                    match = re.search(r'(\d{5,6})$', name_without_ext)
                    if match:
                        return int(match.group(1))
                    match = re.search(r'(\d+)', name_without_ext)
                    return int(match.group(1)) if match else 0

                frame_files.sort(key=get_frame_num)

                # Filter frames starting from start_frame
                filtered_frames = []
                for frame_file in frame_files:
                    frame_num = get_frame_num(frame_file)
                    if frame_num >= start_frame:
                        filtered_frames.append(frame_file)

                return filtered_frames

    return []


def create_side_by_side_frames(original_frames, predicted_frames, temp_dir):
    """Create side-by-side frames for ffmpeg processing"""
    if not original_frames or not predicted_frames:
        return []

    # Ensure we have the same number of frames
    min_frames = min(len(original_frames), len(predicted_frames))
    if min_frames == 0:
        return []

    side_by_side_frames = []

    # Read first frame to get dimensions
    first_original = cv2.imread(original_frames[0])
    first_predicted = cv2.imread(predicted_frames[0])

    if first_original is None or first_predicted is None:
        return []

    # Get dimensions
    h, w = first_original.shape[:2]

    for i in range(min_frames):
        try:
            # Read frames
            original_frame = cv2.imread(original_frames[i])
            predicted_frame = cv2.imread(predicted_frames[i])

            if original_frame is None or predicted_frame is None:
                continue

            # Resize predicted to match original if needed
            if predicted_frame.shape[:2] != (h, w):
                predicted_frame = cv2.resize(predicted_frame, (w, h))

            # Create side-by-side frame
            side_by_side = np.hstack([original_frame, predicted_frame])

            # Save temporary frame
            temp_frame_path = os.path.join(temp_dir, f"frame_{i:05d}.png")
            cv2.imwrite(temp_frame_path, side_by_side)
            side_by_side_frames.append(temp_frame_path)

        except Exception as e:
            print(f"⚠️  Error processing frame {i}: {e}")
            continue

    return side_by_side_frames


def get_video_sequences_from_inference(inference_dir, test_dir):
    """Extract video sequences from inference results"""
    print("🔍 Analyzing inference results...")

    # Load inference results
    results_file = os.path.join(inference_dir, 'inference_results.json')
    if not os.path.exists(results_file):
        print(f"❌ Inference results not found: {results_file}")
        return {}

    with open(results_file, 'r') as f:
        inference_results = json.load(f)

    print(f"✅ Loaded inference results: {inference_results.get('total_videos', 0)} videos")

    video_sequences = {}

    # Get video results from inference
    video_results = inference_results.get('video_results', {})

    for video_name, video_info in video_results.items():
        print(f"📹 Processing video: {video_name} ({video_info['count']} frames)")

        # Get predicted frames (sorted by frame number)
        predicted_frames = []
        for frame_info in sorted(video_info['frames'], key=lambda x: x['frame_number']):
            predicted_frames.append(frame_info['path'])

        # Get original frames from test dataset
        original_frames = get_original_frames(test_dir, video_name, start_frame=2)

        if not original_frames:
            print(f"⚠️  No original frames found for {video_name}")
            continue

        # Ensure we have matching frames
        min_frames = min(len(original_frames), len(predicted_frames))
        if min_frames == 0:
            print(f"⚠️  No matching frames for {video_name}")
            continue

        video_sequences[video_name] = {
            'name': video_name,
            'original_frames': original_frames[:min_frames],
            'predicted_frames': predicted_frames[:min_frames],
            'num_frames': min_frames
        }

        print(f"✅ {video_name}: {min_frames} matching frames")

    return video_sequences


def create_video_with_ffmpeg(frames_list, output_video_path, fps=12, quality='medium'):
    """Create video from frame list using ffmpeg"""
    if not check_ffmpeg():
        raise RuntimeError("ffmpeg not found. Please install ffmpeg.")

    # Quality settings
    quality_settings = {
        'high': ['-crf', '18', '-preset', 'slow'],
        'medium': ['-crf', '23', '-preset', 'medium'],
        'low': ['-crf', '28', '-preset', 'fast']
    }

    # Create temporary directory for symlinks/copies
    temp_dir = os.path.join(os.path.dirname(output_video_path), 'temp_frames')
    os.makedirs(temp_dir, exist_ok=True)

    try:
        # Create numbered symlinks for ffmpeg
        for i, frame_path in enumerate(frames_list):
            temp_frame = os.path.join(temp_dir, f"frame_{i:05d}.png")
            if os.path.exists(temp_frame):
                os.remove(temp_frame)

            # Copy file instead of symlink for Windows compatibility
            import shutil
            shutil.copy2(frame_path, temp_frame)

        # Input pattern for frames
        input_pattern = os.path.join(temp_dir, "frame_%05d.png")

        # FFmpeg command
        cmd = [
            'ffmpeg',
            '-y',  # Overwrite output file
            '-framerate', str(fps),
            '-i', input_pattern,
            '-c:v', 'libx264',
            '-pix_fmt', 'yuv420p',
        ] + quality_settings.get(quality, quality_settings['medium']) + [
            output_video_path
        ]

        print(f"🎬 Creating video: {os.path.basename(output_video_path)}")
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)

        # Clean up temp directory
        import shutil
        shutil.rmtree(temp_dir)

        return True

    except subprocess.CalledProcessError as e:
        print(f"❌ FFmpeg error: {e.stderr}")
        # Clean up temp directory on error
        import shutil
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
        return False
    except Exception as e:
        print(f"❌ Error creating video: {e}")
        # Clean up temp directory on error
        import shutil
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
        return False


def process_video_sequences(video_sequences, output_dir, fps=12, quality='medium'):
    """Process all video sequences and create side-by-side comparison videos using ffmpeg"""
    video_results = {}

    print(f"🎬 Creating side-by-side comparison videos...")
    print(f"   - Format: Original | Predicted")
    print(f"   - FPS: {fps}")
    print(f"   - Quality: {quality}")
    print(f"   - Output: {output_dir}")

    for video_name, sequence_info in tqdm(video_sequences.items(), desc="Creating videos"):
        try:
            # Create video output directory
            video_output_dir = os.path.join(output_dir, video_name)
            os.makedirs(video_output_dir, exist_ok=True)

            # Output video path
            video_output_path = os.path.join(video_output_dir, f"{video_name}_original_vs_predicted.mp4")

            # Create temporary directory for side-by-side frames
            temp_dir = os.path.join(video_output_dir, 'temp_frames')
            os.makedirs(temp_dir, exist_ok=True)

            try:
                # Create side-by-side frames
                side_by_side_frames = create_side_by_side_frames(
                    sequence_info['original_frames'],
                    sequence_info['predicted_frames'],
                    temp_dir
                )

                if not side_by_side_frames:
                    video_results[video_name] = {
                        'status': 'failed',
                        'reason': 'no_frames_created'
                    }
                    print(f"❌ No frames created for {video_name}")
                    continue

                # Create video using ffmpeg directly from side-by-side frames
                input_pattern = os.path.join(temp_dir, "frame_%05d.png").replace('\\', '/')

                # Quality settings
                quality_settings = {
                    'high': ['-crf', '18', '-preset', 'slow'],
                    'medium': ['-crf', '23', '-preset', 'medium'],
                    'low': ['-crf', '28', '-preset', 'fast']
                }

                # FFmpeg command
                cmd = [
                    'ffmpeg',
                    '-y',  # Overwrite output file
                    '-framerate', str(fps),
                    '-i', input_pattern,
                    '-c:v', 'libx264',
                    '-pix_fmt', 'yuv420p',
                ] + quality_settings.get(quality, quality_settings['medium']) + [
                    video_output_path
                ]

                try:
                    result = subprocess.run(cmd, capture_output=True, text=True, check=True)
                    video_created = True
                except subprocess.CalledProcessError as e:
                    print(f"❌ FFmpeg error: {e.stderr}")
                    video_created = False
                except Exception as e:
                    print(f"❌ Error running ffmpeg: {e}")
                    video_created = False

                if video_created:
                    video_results[video_name] = {
                        'status': 'success',
                        'video_path': video_output_path,
                        'num_frames': len(side_by_side_frames),
                        'fps': fps,
                        'quality': quality,
                        'format': 'original_vs_predicted'
                    }
                    print(f"✅ Created: {os.path.basename(video_output_path)}")
                else:
                    video_results[video_name] = {
                        'status': 'failed',
                        'reason': 'ffmpeg_failed'
                    }
                    print(f"❌ FFmpeg failed for {video_name}")

            finally:
                # Clean up temp directory
                import shutil
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir)

        except Exception as e:
            video_results[video_name] = {
                'status': 'failed',
                'reason': str(e)
            }
            print(f"❌ Error processing {video_name}: {e}")

    return video_results


def create_summary_report(video_results, output_dir):
    """Create a summary report of video creation results"""
    summary = {
        'timestamp': datetime.now().isoformat(),
        'total_videos': len(video_results),
        'successful_videos': len([r for r in video_results.values() if r['status'] == 'success']),
        'failed_videos': len([r for r in video_results.values() if r['status'] == 'failed']),
        'results': video_results
    }

    # Save summary
    summary_file = os.path.join(output_dir, 'video_creation_summary.json')
    with open(summary_file, 'w') as f:
        json.dump(summary, f, indent=2)

    # Print summary
    print(f"\n📊 Video Creation Summary:")
    print(f"   - Total videos: {summary['total_videos']}")
    print(f"   - Successful: {summary['successful_videos']}")
    print(f"   - Failed: {summary['failed_videos']}")
    print(f"   - Success rate: {summary['successful_videos']/summary['total_videos']*100:.1f}%")
    print(f"   - Summary saved: {summary_file}")

    return summary


def main():
    parser = argparse.ArgumentParser(description='Create comparison videos from Original SDCNet inference results')
    parser.add_argument('--inference_dir', required=True,
                       help='Directory containing inference results')
    parser.add_argument('--test_dir', default='./dataset/test',
                       help='Path to test dataset directory')
    parser.add_argument('--output_dir', default=None,
                       help='Output directory for videos (default: inference_dir/videos)')
    parser.add_argument('--fps', type=int, default=12,
                       help='Video frame rate (default: 12 for slower playback)')
    parser.add_argument('--quality', choices=['low', 'medium', 'high'], default='medium',
                       help='Video quality (default: medium)')

    args = parser.parse_args()

    print("🎬 Original SDCNet Video Creator")
    print("=" * 50)

    # Check ffmpeg
    if not check_ffmpeg():
        print("❌ Error: ffmpeg not found. Please install ffmpeg to create videos.")
        print("   Download from: https://ffmpeg.org/download.html")
        print("   Windows: Download and add to PATH")
        print("   Linux: sudo apt install ffmpeg")
        print("   macOS: brew install ffmpeg")
        sys.exit(1)

    print("✅ ffmpeg found")

    # Validate input directory
    if not os.path.exists(args.inference_dir):
        print(f"❌ Inference directory not found: {args.inference_dir}")
        sys.exit(1)

    # Set output directory
    if args.output_dir is None:
        args.output_dir = os.path.join(args.inference_dir, 'videos')

    os.makedirs(args.output_dir, exist_ok=True)

    print(f"📁 Input: {args.inference_dir}")
    print(f"📁 Output: {args.output_dir}")
    print(f"🎥 Settings: {args.fps}fps, {args.quality} quality")

    # Get video sequences from inference results
    video_sequences = get_video_sequences_from_inference(args.inference_dir, args.test_dir)

    if not video_sequences:
        print("❌ No video sequences found in inference results")
        sys.exit(1)

    print(f"🎬 Found {len(video_sequences)} video sequences to process")

    # Create videos
    video_results = process_video_sequences(
        video_sequences,
        args.output_dir,
        args.fps,
        args.quality
    )

    # Create summary report
    summary = create_summary_report(video_results, args.output_dir)

    # Final message
    if summary['successful_videos'] > 0:
        print(f"\n🎉 Video creation completed!")
        print(f"   Check videos in: {args.output_dir}")
        print(f"\n💡 Video Format:")
        print(f"   - Left side: Original frames (from test dataset)")
        print(f"   - Right side: Predicted frames (from SDCNet)")
        print(f"   - Synchronized starting from frame 00002")
        print(f"   - {args.fps}fps for detailed analysis")
        print(f"\n📁 Each video is in its own subdirectory:")
        for video_name in video_sequences.keys():
            print(f"   - {args.output_dir}/{video_name}/{video_name}_original_vs_predicted.mp4")
    else:
        print(f"\n❌ No videos were created successfully")
        print(f"   Check the error messages above")


if __name__ == "__main__":
    main()
