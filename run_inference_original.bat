@echo off
echo ========================================
echo Original SDCNet Inference Pipeline
echo ========================================
echo.
echo This script runs inference on the test set using trained Original SDCNet models
echo and creates comparison videos showing Input ^| Prediction ^| Target frames.
echo.

:MENU
echo Please choose inference type:
echo [1] Automatic Inference (recommended - auto-detects best checkpoint)
echo [2] Manual Inference (specify checkpoint manually)
echo [3] List Available Checkpoints
echo [4] Inference Only (skip video creation)
echo [Q] Quit
echo.
set /p choice="Enter your choice (1/2/3/4/Q): "

if /i "%choice%"=="1" goto AUTO
if /i "%choice%"=="2" goto MANUAL
if /i "%choice%"=="3" goto LIST
if /i "%choice%"=="4" goto INFERENCE_ONLY
if /i "%choice%"=="Q" goto END
if /i "%choice%"=="q" goto END

echo Invalid choice. Please try again.
echo.
goto MENU

:AUTO
echo.
echo ========================================
echo Automatic Inference Pipeline
echo ========================================
echo.
echo This will:
echo 1. Auto-detect the best Original SDCNet checkpoint
echo 2. Run inference on the entire test set
echo 3. Create comparison videos (Input ^| Prediction ^| Target)
echo.
echo Configuration:
echo - Test dataset: .\dataset\test\
echo - Output: .\test_results\
echo - Video FPS: 12 (slower for detailed analysis)
echo - Video quality: medium
echo.

python run_inference_auto_original.py ^
    --test_dir .\dataset\test ^
    --output_dir .\test_results ^
    --fps 12

goto COMPLETE

:MANUAL
echo.
echo ========================================
echo Manual Inference Pipeline
echo ========================================
echo.
echo Please specify the checkpoint path:
set /p checkpoint_path="Checkpoint path (e.g., .\checkpoints\sdcnet_original_best.pth): "

if "%checkpoint_path%"=="" (
    echo Error: No checkpoint path provided.
    goto MENU
)

if not exist "%checkpoint_path%" (
    echo Error: Checkpoint file not found: %checkpoint_path%
    goto MENU
)

echo.
echo Running inference with checkpoint: %checkpoint_path%
echo.

python run_inference_auto_original.py ^
    --checkpoint "%checkpoint_path%" ^
    --test_dir .\dataset\test ^
    --output_dir .\test_results ^
    --fps 12

goto COMPLETE

:LIST
echo.
echo ========================================
echo Available Checkpoints
echo ========================================
echo.

python run_inference_auto_original.py --list_checkpoints

echo.
pause
goto MENU

:INFERENCE_ONLY
echo.
echo ========================================
echo Inference Only (No Videos)
echo ========================================
echo.
echo This will run inference but skip video creation.
echo Useful for quick testing or when you only need the prediction images.
echo.

python run_inference_auto_original.py ^
    --test_dir .\dataset\test ^
    --output_dir .\test_results ^
    --skip_videos

goto COMPLETE

:COMPLETE
echo.
if %ERRORLEVEL% EQU 0 (
    echo ========================================
    echo 🎉 Inference Pipeline Completed Successfully!
    echo ========================================
    echo.
    echo Results have been saved to: .\test_results\
    echo.
    echo What was created:
    echo ✓ Prediction images (frame-by-frame predictions)
    echo ✓ Comparison images (Input ^| Prediction ^| Target)
    echo ✓ Comparison videos (if not skipped)
    echo ✓ Inference statistics and summary
    echo.
    echo Next steps:
    echo 1. Check the results in .\test_results\inference_TIMESTAMP\
    echo 2. Watch comparison videos in .\test_results\inference_TIMESTAMP\videos\
    echo 3. Compare with SDCNet with masks results
    echo.
    echo Video format: Side-by-side comparison showing:
    echo - Left: Input frame (t-1)
    echo - Center: Predicted frame (t+1)
    echo - Right: Target frame (ground truth t+1)
    echo.
) else (
    echo ========================================
    echo ❌ Inference Pipeline Failed!
    echo ========================================
    echo.
    echo Please check:
    echo 1. Test dataset exists at .\dataset\test\
    echo 2. Test dataset has correct structure (X/ and Y/ subdirectories)
    echo 3. Trained model checkpoint exists
    echo 4. GPU memory is sufficient
    echo 5. All dependencies are installed
    echo.
    echo For troubleshooting:
    echo - Run: python run_inference_auto_original.py --list_checkpoints
    echo - Check test dataset structure
    echo - Verify FlowNet2 checkpoint exists
    echo.
)

:END
echo.
echo Press any key to exit...
pause >nul
