<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SDCNet Results Comparison Viewer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1a1a1a;
            color: #ffffff;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #4CAF50;
            margin-bottom: 10px;
        }

        .controls {
            background: #2d2d2d;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            align-items: center;
            justify-content: center;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
        }

        .control-group label {
            font-size: 12px;
            color: #cccccc;
            text-transform: uppercase;
        }

        select, input, button {
            padding: 8px 12px;
            border: none;
            border-radius: 5px;
            background: #404040;
            color: #ffffff;
            font-size: 14px;
        }

        select:focus, input:focus {
            outline: 2px solid #4CAF50;
        }

        button {
            background: #4CAF50;
            cursor: pointer;
            transition: background 0.3s;
        }

        button:hover {
            background: #45a049;
        }

        button:disabled {
            background: #666666;
            cursor: not-allowed;
        }

        .frame-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .grid-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .viewer-container {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .ground-truth-section {
            text-align: center;
        }

        .predictions-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .display-container {
            background: #2d2d2d;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }

        .display-title {
            color: #4CAF50;
            margin-bottom: 10px;
            font-weight: bold;
            font-size: 14px;
        }

        .image-container {
            position: relative;
            background: #000000;
            border-radius: 5px;
            overflow: hidden;
            display: inline-block;
            max-width: 100%;
        }

        .frame-image {
            max-width: 100%;
            height: auto;
            display: block;
        }

        .grid-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            opacity: 0.7;
        }

        .status {
            text-align: center;
            padding: 20px;
            color: #cccccc;
            font-style: italic;
        }

        .error {
            color: #ff6b6b;
        }

        .frame-info {
            margin-top: 10px;
            font-size: 12px;
            color: #888888;
        }

        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                gap: 15px;
            }

            .predictions-section {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎬 SDCNet Results Comparison Viewer</h1>
        <p>Visual comparison of SDCNet variants: Original vs Masked, No Skip vs Skip Training</p>
    </div>

    <div class="controls">
        <div class="control-group">
            <label>Video Selection</label>
            <select id="videoSelect">
                <option value="">Select a video...</option>
            </select>
        </div>

        <div class="control-group">
            <label>Inference Results</label>
            <select id="inferenceSelect">
                <option value="">Select inference results...</option>
            </select>
        </div>

        <div class="frame-controls">
            <button id="prevFrame" disabled>◀ Previous</button>
            <div class="control-group">
                <label>Frame</label>
                <input type="number" id="frameInput" min="0" value="0" disabled>
            </div>
            <button id="nextFrame" disabled>Next ▶</button>
        </div>

        <div class="grid-controls">
            <div class="control-group">
                <label>Grid Size</label>
                <input type="range" id="gridSize" min="1" max="10" value="1">
                <span id="gridSizeLabel">1x1</span>
            </div>
            <div class="control-group">
                <label>Grid Color</label>
                <input type="color" id="gridColor" value="#ff0000">
            </div>
        </div>
    </div>

    <div class="viewer-container">
        <div class="ground-truth-section">
            <div class="display-container">
                <div class="display-title">Ground Truth</div>
                <div class="image-container" id="groundTruthContainer">
                    <img id="groundTruthImage" class="frame-image" style="display: none;">
                    <canvas class="grid-overlay" id="groundTruthGrid"></canvas>
                </div>
                <div class="frame-info" id="groundTruthInfo"></div>
            </div>
        </div>

        <div class="predictions-section">
            <div class="display-container">
                <div class="display-title">SDCNet Original (No Skip)</div>
                <div class="image-container" id="originalNoSkipContainer">
                    <img id="originalNoSkipImage" class="frame-image" style="display: none;">
                    <canvas class="grid-overlay" id="originalNoSkipGrid"></canvas>
                </div>
                <div class="frame-info" id="originalNoSkipInfo"></div>
            </div>

            <div class="display-container">
                <div class="display-title">SDCNet Original (With Skip)</div>
                <div class="image-container" id="originalSkipContainer">
                    <img id="originalSkipImage" class="frame-image" style="display: none;">
                    <canvas class="grid-overlay" id="originalSkipGrid"></canvas>
                </div>
                <div class="frame-info" id="originalSkipInfo"></div>
            </div>

            <div class="display-container">
                <div class="display-title">SDCNet Masked (No Skip)</div>
                <div class="image-container" id="maskedNoSkipContainer">
                    <img id="maskedNoSkipImage" class="frame-image" style="display: none;">
                    <canvas class="grid-overlay" id="maskedNoSkipGrid"></canvas>
                </div>
                <div class="frame-info" id="maskedNoSkipInfo"></div>
            </div>

            <div class="display-container">
                <div class="display-title">SDCNet Masked (With Skip)</div>
                <div class="image-container" id="maskedSkipContainer">
                    <img id="maskedSkipImage" class="frame-image" style="display: none;">
                    <canvas class="grid-overlay" id="maskedSkipGrid"></canvas>
                </div>
                <div class="frame-info" id="maskedSkipInfo"></div>
            </div>
        </div>
    </div>

    <div class="status" id="status">
        Please select a video and inference results to begin comparison.
    </div>

    <script>
        // Application state
        let currentVideo = '';
        let currentInference = '';
        let currentFrame = 0;
        let maxFrames = 0;
        let availableFrames = [];

        // DOM elements
        const videoSelect = document.getElementById('videoSelect');
        const inferenceSelect = document.getElementById('inferenceSelect');
        const frameInput = document.getElementById('frameInput');
        const prevButton = document.getElementById('prevFrame');
        const nextButton = document.getElementById('nextFrame');
        const gridSizeSlider = document.getElementById('gridSize');
        const gridSizeLabel = document.getElementById('gridSizeLabel');
        const gridColorPicker = document.getElementById('gridColor');
        const status = document.getElementById('status');

        // Image elements
        const images = {
            groundTruth: document.getElementById('groundTruthImage'),
            originalNoSkip: document.getElementById('originalNoSkipImage'),
            originalSkip: document.getElementById('originalSkipImage'),
            maskedNoSkip: document.getElementById('maskedNoSkipImage'),
            maskedSkip: document.getElementById('maskedSkipImage')
        };

        // Grid canvas elements
        const grids = {
            groundTruth: document.getElementById('groundTruthGrid'),
            originalNoSkip: document.getElementById('originalNoSkipGrid'),
            originalSkip: document.getElementById('originalSkipGrid'),
            maskedNoSkip: document.getElementById('maskedNoSkipGrid'),
            maskedSkip: document.getElementById('maskedSkipGrid')
        };

        // Info elements
        const infos = {
            groundTruth: document.getElementById('groundTruthInfo'),
            originalNoSkip: document.getElementById('originalNoSkipInfo'),
            originalSkip: document.getElementById('originalSkipInfo'),
            maskedNoSkip: document.getElementById('maskedNoSkipInfo'),
            maskedSkip: document.getElementById('maskedSkipInfo')
        };

        // Initialize the application
        async function init() {
            await loadVideoList();
            await loadInferenceList();
            setupEventListeners();
            updateGridSizeLabel();
        }

        // Load available videos from the test dataset
        async function loadVideoList() {
            try {
                const response = await fetch('/api/videos');
                const data = await response.json();

                if (data.error) {
                    throw new Error(data.message);
                }

                videoSelect.innerHTML = '<option value="">Select a video...</option>';
                data.videos.forEach(video => {
                    const option = document.createElement('option');
                    option.value = video;
                    option.textContent = video;
                    videoSelect.appendChild(option);
                });
            } catch (error) {
                console.error('Error loading video list:', error);
                status.textContent = 'Error loading video list. Make sure the server is running.';
                status.className = 'status error';
            }
        }

        // Load available inference results
        async function loadInferenceList() {
            try {
                const response = await fetch('/api/inference');
                const data = await response.json();

                if (data.error) {
                    throw new Error(data.message);
                }

                inferenceSelect.innerHTML = '<option value="">Select inference results...</option>';
                data.inference_results.forEach(inference => {
                    const option = document.createElement('option');
                    option.value = inference.id;
                    option.textContent = inference.display_name;
                    option.title = inference.description;
                    inferenceSelect.appendChild(option);
                });
            } catch (error) {
                console.error('Error loading inference list:', error);
                status.textContent = 'Error loading inference results. Make sure the server is running.';
                status.className = 'status error';
            }
        }

        // Setup event listeners
        function setupEventListeners() {
            videoSelect.addEventListener('change', onVideoChange);
            inferenceSelect.addEventListener('change', onInferenceChange);
            frameInput.addEventListener('change', onFrameChange);
            prevButton.addEventListener('click', () => changeFrame(-1));
            nextButton.addEventListener('click', () => changeFrame(1));
            gridSizeSlider.addEventListener('input', onGridSizeChange);
            gridColorPicker.addEventListener('change', updateGrids);

            // Keyboard navigation
            document.addEventListener('keydown', (e) => {
                if (e.key === 'ArrowLeft') {
                    e.preventDefault();
                    changeFrame(-1);
                } else if (e.key === 'ArrowRight') {
                    e.preventDefault();
                    changeFrame(1);
                }
            });
        }

        // Handle video selection change
        async function onVideoChange() {
            currentVideo = videoSelect.value;
            if (currentVideo && currentInference) {
                await loadVideoData();
            }
        }

        // Handle inference selection change
        async function onInferenceChange() {
            currentInference = inferenceSelect.value;
            if (currentVideo && currentInference) {
                await loadVideoData();
            }
        }

        // Load video data and available frames
        async function loadVideoData() {
            try {
                status.textContent = 'Loading video data...';
                status.className = 'status';

                // Reset frame counter
                currentFrame = 0;
                availableFrames = [];

                // Query server for available frames
                const response = await fetch(`/api/frames?video=${encodeURIComponent(currentVideo)}&inference=${encodeURIComponent(currentInference)}`);
                const data = await response.json();

                if (data.error) {
                    throw new Error(data.message);
                }

                availableFrames = data.frames;
                maxFrames = availableFrames.length;

                if (maxFrames > 0) {
                    frameInput.max = maxFrames - 1;
                    frameInput.disabled = false;
                    prevButton.disabled = false;
                    nextButton.disabled = false;

                    await loadFrame(0);
                    status.textContent = `Loaded ${maxFrames} frames for ${currentVideo} (${data.range.start}-${data.range.end})`;
                } else {
                    status.textContent = 'No frames found for this video/inference combination';
                    status.className = 'status error';
                }
            } catch (error) {
                console.error('Error loading video data:', error);
                status.textContent = 'Error loading video data. Check if the video and inference results exist.';
                status.className = 'status error';
            }
        }

        // Handle frame input change
        function onFrameChange() {
            const newFrame = parseInt(frameInput.value);
            if (newFrame >= 0 && newFrame < maxFrames) {
                loadFrame(newFrame);
            }
        }

        // Change frame by delta
        function changeFrame(delta) {
            const newFrame = currentFrame + delta;
            if (newFrame >= 0 && newFrame < maxFrames) {
                frameInput.value = newFrame;
                loadFrame(newFrame);
            }
        }

        // Load and display a specific frame
        async function loadFrame(frameIndex) {
            if (frameIndex < 0 || frameIndex >= maxFrames) return;

            currentFrame = frameIndex;
            const actualFrameNumber = availableFrames[frameIndex];

            // Update frame input
            frameInput.value = frameIndex;

            // Update button states
            prevButton.disabled = frameIndex === 0;
            nextButton.disabled = frameIndex === maxFrames - 1;

            // Load images for all variants
            await Promise.all([
                loadImage('groundTruth', `dataset/test/Y/${currentVideo}/${actualFrameNumber.toString().padStart(5, '0')}.png`),
                loadImage('originalNoSkip', `test_results/${currentInference}/${currentVideo}/${actualFrameNumber.toString().padStart(5, '0')}.png`),
                loadImage('originalSkip', `test_results/${currentInference}_skip/${currentVideo}/${actualFrameNumber.toString().padStart(5, '0')}.png`),
                loadImage('maskedNoSkip', `test_results/${currentInference}_masked/${currentVideo}/pred_${actualFrameNumber.toString().padStart(5, '0')}.png`),
                loadImage('maskedSkip', `test_results/${currentInference}_masked_skip/${currentVideo}/pred_${actualFrameNumber.toString().padStart(5, '0')}.png`)
            ]);

            // Update frame info
            Object.keys(infos).forEach(key => {
                infos[key].textContent = `Frame ${frameIndex + 1}/${maxFrames} (${actualFrameNumber})`;
            });

            // Update grids
            updateGrids();
        }

        // Load a single image
        async function loadImage(type, src) {
            return new Promise((resolve) => {
                const img = images[type];
                const info = infos[type];

                img.onload = () => {
                    img.style.display = 'block';
                    // Resize canvas to match image
                    const canvas = grids[type];
                    canvas.width = img.naturalWidth;
                    canvas.height = img.naturalHeight;
                    canvas.style.width = img.offsetWidth + 'px';
                    canvas.style.height = img.offsetHeight + 'px';
                    resolve();
                };

                img.onerror = () => {
                    img.style.display = 'none';
                    info.textContent = 'Image not found';
                    resolve();
                };

                img.src = src;
            });
        }

        // Handle grid size change
        function onGridSizeChange() {
            updateGridSizeLabel();
            updateGrids();
        }

        // Update grid size label
        function updateGridSizeLabel() {
            const size = gridSizeSlider.value;
            gridSizeLabel.textContent = `${size}x${size}`;
        }

        // Update all grid overlays
        function updateGrids() {
            const gridSize = parseInt(gridSizeSlider.value);
            const gridColor = gridColorPicker.value;

            Object.keys(grids).forEach(key => {
                drawGrid(grids[key], gridSize, gridColor);
            });
        }

        // Draw grid on canvas
        function drawGrid(canvas, gridSize, color) {
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            if (gridSize <= 1) return;

            ctx.strokeStyle = color;
            ctx.lineWidth = 2;

            const cellWidth = canvas.width / gridSize;
            const cellHeight = canvas.height / gridSize;

            // Draw vertical lines
            for (let i = 1; i < gridSize; i++) {
                const x = i * cellWidth;
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, canvas.height);
                ctx.stroke();
            }

            // Draw horizontal lines
            for (let i = 1; i < gridSize; i++) {
                const y = i * cellHeight;
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(canvas.width, y);
                ctx.stroke();
            }
        }

        // Initialize the application when the page loads
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
