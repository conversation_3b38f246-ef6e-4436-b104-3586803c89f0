#!/usr/bin/env python3
"""
Test script for skip augmentation functionality.
This script tests the new skip frame augmentation feature in FrameLoaderWithMasks.
"""

import os
import sys
import argparse
import torch
from torch.utils.data import DataLoader

# Add current directory to path
sys.path.append('.')

from datasets.frame_loader_with_masks import FrameLoaderWithMasks

def create_test_args():
    """Create test arguments for the dataset loader"""
    parser = argparse.ArgumentParser()
    
    # Required arguments
    parser.add_argument('--sequence_length', default=2, type=int)
    parser.add_argument('--sample_rate', default=1, type=int)
    parser.add_argument('--crop_size', default=[256, 320], nargs=2, type=int)
    parser.add_argument('--start_index', default=0, type=int)
    parser.add_argument('--stride', default=64, type=int)
    parser.add_argument('--skip_augmentation', action='store_true')
    
    return parser.parse_args([])

def test_dataset_loading(dataset_path, skip_augmentation=False):
    """Test dataset loading with and without skip augmentation"""
    
    print(f"\n{'='*60}")
    print(f"Testing dataset: {dataset_path}")
    print(f"Skip augmentation: {'enabled' if skip_augmentation else 'disabled'}")
    print(f"{'='*60}")
    
    # Create test arguments
    args = create_test_args()
    args.skip_augmentation = skip_augmentation
    
    try:
        # Create dataset
        dataset = FrameLoaderWithMasks(args, dataset_path, is_training=True)
        
        print(f"Dataset size: {len(dataset)}")
        
        if skip_augmentation and hasattr(dataset, 'consecutive_total'):
            print(f"  - Consecutive sequences: {dataset.consecutive_total}")
            print(f"  - Skip sequences: {dataset.skip_total}")
            print(f"  - Total: {dataset.consecutive_total + dataset.skip_total}")
        
        # Test first few samples
        print(f"\nTesting first 5 samples:")
        for i in range(min(5, len(dataset))):
            try:
                sample = dataset[i]
                sequence_type = sample.get('sequence_type', 'unknown')
                video_name = sample.get('video_name', 'unknown')
                
                print(f"  Sample {i}: {sequence_type} sequence from {video_name}")
                print(f"    - Frames: {len(sample['image'])}")
                print(f"    - Masks: {len(sample['mask'])}")
                print(f"    - Frame files: {[os.path.basename(f) for f in sample['input_files']]}")
                
            except Exception as e:
                print(f"  Sample {i}: ERROR - {e}")
        
        # Test with DataLoader
        print(f"\nTesting with DataLoader (batch_size=2):")
        loader = DataLoader(dataset, batch_size=2, shuffle=False, num_workers=0)
        
        for i, batch in enumerate(loader):
            if i >= 2:  # Test only first 2 batches
                break
                
            print(f"  Batch {i}:")
            print(f"    - Batch size: {len(batch['image'])}")
            print(f"    - Sequence types: {batch.get('sequence_type', ['unknown'])}")
            print(f"    - Video names: {batch.get('video_name', ['unknown'])}")
            
        print(f"✅ Dataset loading successful!")
        return True
        
    except Exception as e:
        print(f"❌ Dataset loading failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    parser = argparse.ArgumentParser(description='Test skip augmentation functionality')
    parser.add_argument('--dataset_path', required=True, type=str,
                       help='Path to dataset directory (should contain X and Y subdirectories)')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.dataset_path):
        print(f"❌ Dataset path does not exist: {args.dataset_path}")
        return
    
    print("🧪 Testing Skip Augmentation Functionality")
    print("=" * 60)
    
    # Test without skip augmentation (baseline)
    success_baseline = test_dataset_loading(args.dataset_path, skip_augmentation=False)
    
    # Test with skip augmentation
    success_skip = test_dataset_loading(args.dataset_path, skip_augmentation=True)
    
    # Summary
    print(f"\n{'='*60}")
    print("TEST SUMMARY")
    print(f"{'='*60}")
    print(f"Baseline (consecutive only): {'✅ PASS' if success_baseline else '❌ FAIL'}")
    print(f"Skip augmentation: {'✅ PASS' if success_skip else '❌ FAIL'}")
    
    if success_baseline and success_skip:
        print(f"\n🎉 All tests passed! Skip augmentation is working correctly.")
        print(f"\nTo use skip augmentation in training, add the --skip_augmentation flag:")
        print(f"python train_sdcnet_with_masks.py --skip_augmentation --train_file {args.dataset_path} --val_file <val_path> ...")
    else:
        print(f"\n❌ Some tests failed. Please check the dataset structure and implementation.")

if __name__ == "__main__":
    main()
