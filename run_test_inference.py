#!/usr/bin/env python3
"""
Complete Test Inference Pipeline
Runs inference on test set and creates comparison videos in one go.
"""

import os
import sys
import argparse
import subprocess
import json
from datetime import datetime


def run_command(cmd, description=""):
    """Run a command and handle errors"""
    print(f"\n{'='*50}")
    print(f"Running: {description}")
    print(f"Command: {' '.join(cmd)}")
    print(f"{'='*50}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✓ Success!")
        if result.stdout:
            print("Output:", result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Error: {e}")
        if e.stderr:
            print("Error details:", e.stderr)
        return False


def check_requirements():
    """Check if all requirements are met"""
    print("Checking requirements...")
    
    # Check Python scripts exist
    required_scripts = [
        'test_inference.py',
        'create_comparison_videos.py'
    ]
    
    for script in required_scripts:
        if not os.path.exists(script):
            print(f"✗ Missing script: {script}")
            return False
        print(f"✓ Found: {script}")
    
    # Check ffmpeg
    try:
        subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True)
        print("✓ ffmpeg is available")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("✗ ffmpeg not found. Please install ffmpeg.")
        print("  Download from: https://ffmpeg.org/download.html")
        return False
    
    return True


def find_best_checkpoint(checkpoint_dir="./checkpoints"):
    """Find the best available checkpoint"""
    import glob
    
    # Look for best model checkpoints first
    patterns = [
        os.path.join(checkpoint_dir, "*best*.pth"),
        os.path.join(checkpoint_dir, "*best*.pth.tar"),
        os.path.join(checkpoint_dir, "*.pth"),
        os.path.join(checkpoint_dir, "*.pth.tar")
    ]
    
    for pattern in patterns:
        checkpoints = glob.glob(pattern)
        if checkpoints:
            # Return the most recent one
            latest = max(checkpoints, key=os.path.getmtime)
            return latest
    
    return None


def main():
    parser = argparse.ArgumentParser(description='Complete test inference pipeline')
    parser.add_argument('--test_dir', default='./dataset/test',
                       help='Path to test dataset directory')
    parser.add_argument('--checkpoint', default=None,
                       help='Path to model checkpoint (auto-detect if not provided)')
    parser.add_argument('--output_dir', default='./test_results',
                       help='Base directory for all outputs')
    parser.add_argument('--gpu', type=int, default=0,
                       help='GPU device ID')
    parser.add_argument('--fps', type=int, default=30,
                       help='Video frame rate')
    parser.add_argument('--quality', choices=['low', 'medium', 'high'], default='medium',
                       help='Video quality')
    parser.add_argument('--create_summary', action='store_true',
                       help='Create summary video with clips from multiple videos')
    parser.add_argument('--skip_inference', action='store_true',
                       help='Skip inference step (use existing results)')
    parser.add_argument('--skip_videos', action='store_true',
                       help='Skip video creation step')
    
    args = parser.parse_args()
    
    # Check requirements
    if not check_requirements():
        print("\nPlease install missing requirements and try again.")
        sys.exit(1)
    
    # Find checkpoint if not provided
    if args.checkpoint is None:
        args.checkpoint = find_best_checkpoint()
        if args.checkpoint is None:
            print("✗ No checkpoint found. Please specify --checkpoint")
            sys.exit(1)
    
    print(f"\nUsing checkpoint: {args.checkpoint}")
    
    # Check if test directory exists
    if not os.path.exists(args.test_dir):
        print(f"✗ Test directory not found: {args.test_dir}")
        sys.exit(1)
    
    # Create timestamp for this run
    timestamp = datetime.now().strftime("%Y_%m_%d_%H_%M_%S")
    
    # Step 1: Run inference
    inference_dir = None
    if not args.skip_inference:
        print(f"\n🚀 Step 1: Running inference on test set...")
        
        inference_cmd = [
            sys.executable, 'test_inference.py',
            '--test_dir', args.test_dir,
            '--checkpoint', args.checkpoint,
            '--output_dir', args.output_dir,
            '--gpu', str(args.gpu)
        ]
        
        if not run_command(inference_cmd, "Test inference"):
            print("✗ Inference failed. Stopping.")
            sys.exit(1)
        
        # Find the inference directory (most recent one)
        import glob
        inference_pattern = os.path.join(args.output_dir, "inference_*")
        inference_dirs = glob.glob(inference_pattern)
        if inference_dirs:
            inference_dir = max(inference_dirs, key=os.path.getmtime)
        else:
            print("✗ Could not find inference results directory")
            sys.exit(1)
    else:
        # Find existing inference directory
        import glob
        inference_pattern = os.path.join(args.output_dir, "inference_*")
        inference_dirs = glob.glob(inference_pattern)
        if inference_dirs:
            inference_dir = max(inference_dirs, key=os.path.getmtime)
            print(f"Using existing inference results: {inference_dir}")
        else:
            print("✗ No existing inference results found. Remove --skip_inference or run inference first.")
            sys.exit(1)
    
    # Step 2: Create comparison videos
    if not args.skip_videos:
        print(f"\n🎬 Step 2: Creating comparison videos...")
        
        video_cmd = [
            sys.executable, 'create_comparison_videos.py',
            '--inference_dir', inference_dir,
            '--test_dir', args.test_dir,
            '--fps', str(args.fps),
            '--quality', args.quality
        ]
        
        if args.create_summary:
            video_cmd.append('--create_summary')
        
        if not run_command(video_cmd, "Video creation"):
            print("✗ Video creation failed.")
            sys.exit(1)
    
    # Summary
    print(f"\n🎉 Pipeline completed successfully!")
    print(f"📁 Results location: {inference_dir}")
    
    if not args.skip_videos:
        videos_dir = os.path.join(inference_dir, 'videos')
        print(f"🎬 Videos location: {videos_dir}")
        
        # List created videos
        if os.path.exists(videos_dir):
            import glob
            video_files = glob.glob(os.path.join(videos_dir, "**", "*.mp4"), recursive=True)
            print(f"📊 Created {len(video_files)} videos:")
            for video in video_files[:10]:  # Show first 10
                rel_path = os.path.relpath(video, videos_dir)
                print(f"   - {rel_path}")
            if len(video_files) > 10:
                print(f"   ... and {len(video_files) - 10} more")
    
    # Create summary report
    summary_file = os.path.join(inference_dir, 'pipeline_summary.json')
    summary = {
        'timestamp': timestamp,
        'checkpoint_used': args.checkpoint,
        'test_dir': args.test_dir,
        'inference_dir': inference_dir,
        'videos_dir': os.path.join(inference_dir, 'videos') if not args.skip_videos else None,
        'settings': {
            'gpu': args.gpu,
            'fps': args.fps,
            'quality': args.quality,
            'create_summary': args.create_summary
        },
        'steps_completed': {
            'inference': not args.skip_inference,
            'videos': not args.skip_videos
        }
    }
    
    with open(summary_file, 'w') as f:
        json.dump(summary, f, indent=2)
    
    print(f"📋 Summary saved to: {summary_file}")
    
    # Instructions for viewing results
    print(f"\n📖 Next steps:")
    print(f"1. Check inference results in: {inference_dir}")
    if not args.skip_videos:
        print(f"2. Watch comparison videos in: {os.path.join(inference_dir, 'videos')}")
        if args.create_summary:
            summary_video = os.path.join(inference_dir, 'videos', 'summary_comparison.mp4')
            if os.path.exists(summary_video):
                print(f"3. Quick overview: {summary_video}")
    
    print(f"\n✨ All done! Happy analyzing! ✨")


if __name__ == "__main__":
    main()
