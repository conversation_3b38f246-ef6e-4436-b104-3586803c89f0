# Original SDCNet Inference System

## 🎯 Overview

This system provides comprehensive inference capabilities for the **Original SDCNet2D model** (without masks), allowing you to:

1. **Run inference** on the entire test set
2. **Create comparison videos** showing Input | Prediction | Target frames
3. **Compare results** with the mask-enhanced version

## 📁 Files Created

### Core Inference Scripts
- **`test_inference_original_sdcnet.py`** - Main inference script for test set
- **`create_comparison_videos_original.py`** - Creates side-by-side comparison videos
- **`run_inference_auto_original.py`** - Automatic pipeline (recommended)
- **`run_inference_original.bat`** - Windows batch script for easy execution

## 🚀 Quick Start

### Option 1: Windows Batch Script (Easiest)
```cmd
# Double-click or run from command prompt
run_inference_original.bat
```

### Option 2: Automatic Pipeline (Recommended)
```bash
# Auto-detect best checkpoint and run complete pipeline
python run_inference_auto_original.py

# Specify test directory
python run_inference_auto_original.py --test_dir ./dataset/test

# Custom output directory
python run_inference_auto_original.py --output_dir ./my_results
```

### Option 3: Manual Control
```bash
# Step 1: Run inference
python test_inference_original_sdcnet.py \
  --checkpoint ./checkpoints/sdcnet_original_best.pth \
  --test_dir ./dataset/test \
  --output_dir ./test_results

# Step 2: Create videos
python create_comparison_videos_original.py \
  --inference_dir ./test_results/inference_TIMESTAMP \
  --fps 12
```

## 📋 Prerequisites

### Required Software
- **Python 3.7+** with PyTorch and project dependencies
- **ffmpeg** for video creation
  - Windows: Download from [ffmpeg.org](https://ffmpeg.org/download.html) and add to PATH
  - Linux: `sudo apt install ffmpeg`
  - macOS: `brew install ffmpeg`

### Required Files
- **Trained model checkpoint** (e.g., `sdcnet_original_best.pth`)
- **Test dataset** with proper structure:
  ```
  dataset/test/
  ├── X/
  │   ├── video_name_1/
  │   │   ├── frame_001.png
  │   │   ├── frame_002.png
  │   │   └── ...
  │   └── video_name_2/
  └── Y/ (optional, for ground truth)
  ```

## 🎬 Output Structure

After running inference, you'll get:

```
test_results/
└── inference_YYYY_MM_DD_HH_MM_SS/
    ├── predictions/           # Individual prediction frames
    │   ├── pred_000001.png
    │   ├── pred_000002.png
    │   └── ...
    ├── comparisons/           # Side-by-side comparison images
    │   ├── comparison_000001.png  # Input | Prediction | Target
    │   ├── comparison_000002.png
    │   └── ...
    ├── targets/               # Ground truth frames
    │   ├── target_000001.png
    │   └── ...
    ├── videos/                # Comparison videos
    │   └── all_sequences/
    │       └── all_sequences_comparison.mp4
    ├── inference_results.json # Inference statistics
    └── video_creation_summary.json # Video creation summary
```

## 🎥 Video Format

Comparison videos show three panels side-by-side:
- **Left**: Input frame (t-1)
- **Center**: Predicted frame (t+1) 
- **Right**: Target frame (ground truth t+1)

### Video Settings
- **Default FPS**: 12 (slower for detailed analysis)
- **Quality**: Medium (good balance of size/quality)
- **Format**: MP4 with H.264 encoding

## ⚙️ Configuration Options

### Inference Parameters
```bash
--checkpoint PATH          # Path to trained model
--test_dir PATH            # Test dataset directory
--output_dir PATH          # Output directory
--batch_size N             # Batch size (default: 1)
--gpu N                    # GPU device ID
--max_videos N             # Limit number of videos (for testing)
```

### Video Parameters
```bash
--fps N                    # Frame rate (default: 12)
--quality LEVEL            # low/medium/high (default: medium)
--skip_videos              # Skip video creation
```

## 🔍 Checkpoint Management

### List Available Checkpoints
```bash
python run_inference_auto_original.py --list_checkpoints
```

### Auto-Detection Priority
1. `*original*best*.pth` files
2. `*sdcnet_original*best*.pth` files  
3. `*original*.pth` files
4. Most recent checkpoint

### Checkpoint Information
The system automatically extracts:
- Training epoch
- Training configuration (batch size, learning rate)
- Skip augmentation status
- Training/validation losses

## 📊 Comparison with Mask Version

### Expected Differences
| Aspect | Original SDCNet | SDCNet with Masks |
|--------|----------------|-------------------|
| **Input Channels** | 8 (6 RGB + 2 flow) | 11 (6 RGB + 3 mask + 2 flow) |
| **Memory Usage** | Lower (~25% less) | Higher |
| **Inference Speed** | Faster | Slower |
| **Performance** | Baseline | Expected better |

### Fair Comparison Guidelines
1. **Same test dataset**: Use identical test sequences
2. **Same inference settings**: Batch size, GPU, etc.
3. **Same video settings**: FPS, quality for visual comparison
4. **Same evaluation metrics**: If using quantitative metrics

## 🛠️ Troubleshooting

### Common Issues

#### No Checkpoints Found
```bash
# Check available checkpoints
python run_inference_auto_original.py --list_checkpoints

# Train a model first if none exist
python train_sdcnet_original.py --train_file ./dataset/train --val_file ./dataset/val
```

#### Test Dataset Structure Error
```
❌ Test directory must contain X/ subdirectory
```
**Solution**: Ensure test dataset has proper structure with `X/` directory containing video folders.

#### FFmpeg Not Found
```
❌ ffmpeg not found. Please install ffmpeg to create videos.
```
**Solution**: Install ffmpeg and add to system PATH.

#### GPU Memory Issues
```bash
# Reduce batch size
--batch_size 1

# Use CPU if necessary (slower)
--gpu -1  # Not implemented yet, use CUDA_VISIBLE_DEVICES=""
```

#### Video Creation Fails
```bash
# Skip video creation and create manually later
python run_inference_auto_original.py --skip_videos

# Then create videos separately
python create_comparison_videos_original.py --inference_dir ./test_results/inference_TIMESTAMP
```

## 📈 Performance Tips

### Faster Inference
- Use `--batch_size 1` (already default for stability)
- Limit videos with `--max_videos N` for testing
- Use `--skip_videos` if you only need prediction images

### Better Videos
- Use `--fps 8` for very slow analysis
- Use `--fps 24` for normal playback speed
- Use `--quality high` for publication-quality videos

## 🎯 Use Cases

### Research Comparison
```bash
# Generate results for both models
python run_inference_auto_original.py --output_dir ./results_original
python run_inference_auto.py --output_dir ./results_with_masks  # For mask version

# Compare videos side-by-side
```

### Quick Testing
```bash
# Test with limited videos
python run_inference_auto_original.py --max_videos 5 --skip_videos
```

### Publication Videos
```bash
# High-quality videos for papers
python create_comparison_videos_original.py \
  --inference_dir ./test_results/inference_TIMESTAMP \
  --fps 12 \
  --quality high
```

## 🎉 Expected Results

### Successful Inference
- **Prediction images**: Individual frame predictions
- **Comparison images**: Side-by-side visual comparisons
- **Statistics**: Success rate, processing time
- **Videos**: Smooth playback showing model performance

### Visual Quality
- **No systematic bias**: Fixed flow bias should eliminate leftward/upward shifts
- **Temporal consistency**: Smooth transitions between frames
- **Detail preservation**: Good reconstruction of fine details

## 📞 Support

If you encounter issues:
1. **Check prerequisites**: Python, PyTorch, ffmpeg
2. **Verify dataset structure**: Proper X/ directory organization
3. **Test with small dataset**: Use `--max_videos 5` first
4. **Check GPU memory**: Reduce batch size if needed
5. **Compare with mask version**: Use same settings for fair comparison

The inference system is designed to work identically to the mask version, providing a fair comparison baseline for your thesis research.
