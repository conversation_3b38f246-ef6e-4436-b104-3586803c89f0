#!/usr/bin/env python3
"""
Example training script with skip augmentation enabled.
This demonstrates how to use the new skip frame augmentation feature.
"""

import os
import sys

# Add current directory to path
sys.path.append('.')

def main():
    """Example training with skip augmentation"""
    
    print("🚀 SDCNet Training with Skip Augmentation")
    print("=" * 60)
    
    # Example command for training with skip augmentation
    train_command = """
python train_sdcnet_with_masks.py \\
    --model SDCNet2DWithMasks \\
    --dataset FrameLoaderWithMasks \\
    --train_file ./dataset/train/ \\
    --val_file ./dataset/val/ \\
    --skip_augmentation \\
    --epochs 100 \\
    --batch_size 4 \\
    --lr 0.0001 \\
    --crop_size 256 320 \\
    --save_dir ./checkpoints \\
    --name sdcnet_with_skip_aug \\
    --patience 10 \\
    --save_freq 5 \\
    --workers 4 \\
    --inference_batches 1 1000 2000 \\
    --inference_samples 3
"""
    
    print("Example training command:")
    print(train_command.strip())
    
    print("\n📊 Expected Results with Skip Augmentation:")
    print("- Dataset size will approximately double")
    print("- Training will include both consecutive (t-1,t→t+1) and skip (t-2,t→t+2) sequences")
    print("- Better performance on fast motion scenarios")
    print("- Longer training time due to increased dataset size")
    
    print("\n🔧 Key Parameters:")
    print("--skip_augmentation    : Enable skip frame augmentation")
    print("--batch_size 4         : Recommended batch size (may need to reduce if memory issues)")
    print("--workers 4            : Number of data loading workers")
    print("--patience 10          : Early stopping patience")
    
    print("\n📁 Dataset Requirements:")
    print("- Videos must have at least 5 frames for skip sequences")
    print("- Standard SDCNet dataset structure with X/ and Y/ directories")
    print("- Binary masks in *_binary_ground_truth directories")
    
    print("\n🧪 Testing Before Training:")
    print("Run the test script first to verify everything works:")
    print("python test_skip_augmentation.py --dataset_path ./dataset/train/")
    
    print("\n⚠️  Important Notes:")
    print("- Skip augmentation only works during training (is_training=True)")
    print("- Validation datasets will use consecutive sequences only")
    print("- Memory usage may increase due to larger effective dataset size")
    print("- Training time will increase proportionally to dataset size increase")
    
    # Check if dataset exists
    if os.path.exists('./dataset/train/'):
        print(f"\n✅ Training dataset found at ./dataset/train/")
        
        # Run test command
        print(f"\n🧪 Testing skip augmentation...")
        test_cmd = "python test_skip_augmentation.py --dataset_path ./dataset/train/"
        print(f"Running: {test_cmd}")
        os.system(test_cmd)
        
    else:
        print(f"\n❌ Training dataset not found at ./dataset/train/")
        print(f"Please ensure your dataset is properly structured.")

if __name__ == "__main__":
    main()
