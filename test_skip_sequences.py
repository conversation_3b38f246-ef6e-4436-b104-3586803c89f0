#!/usr/bin/env python3
"""
Test script to specifically verify skip sequences are loaded correctly.
"""

import os
import sys
import argparse

# Add current directory to path
sys.path.append('.')

from datasets.frame_loader_with_masks import FrameLoaderWithMasks

def create_test_args():
    """Create test arguments for the dataset loader"""
    parser = argparse.ArgumentParser()
    
    # Required arguments
    parser.add_argument('--sequence_length', default=2, type=int)
    parser.add_argument('--sample_rate', default=1, type=int)
    parser.add_argument('--crop_size', default=[256, 320], nargs=2, type=int)
    parser.add_argument('--start_index', default=0, type=int)
    parser.add_argument('--stride', default=64, type=int)
    parser.add_argument('--skip_augmentation', action='store_true')
    
    return parser.parse_args([])

def test_skip_sequences(dataset_path):
    """Test that skip sequences are loaded correctly"""
    
    print("🔍 Testing Skip Sequences Specifically")
    print("=" * 50)
    
    # Create dataset with skip augmentation
    args = create_test_args()
    args.skip_augmentation = True
    
    dataset = FrameLoaderWithMasks(args, dataset_path, is_training=True)
    
    print(f"Total dataset size: {len(dataset)}")
    print(f"Consecutive sequences: {dataset.consecutive_total}")
    print(f"Skip sequences: {dataset.skip_total}")
    
    # Test some skip sequences (they start after consecutive_total)
    skip_start_index = dataset.consecutive_total
    
    print(f"\nTesting skip sequences starting from index {skip_start_index}:")
    
    for i in range(5):  # Test first 5 skip sequences
        index = skip_start_index + i
        if index >= len(dataset):
            break
            
        sample = dataset[index]
        sequence_type = sample.get('sequence_type', 'unknown')
        video_name = sample.get('video_name', 'unknown')
        
        print(f"\nSample {index} (skip sequence {i}):")
        print(f"  - Sequence type: {sequence_type}")
        print(f"  - Video: {video_name}")
        print(f"  - Frame files: {[os.path.basename(f) for f in sample['input_files']]}")
        
        # Verify this is actually a skip sequence
        if len(sample['input_files']) >= 3:
            frame_numbers = []
            for f in sample['input_files']:
                basename = os.path.basename(f)
                # Extract frame number (assuming format like 000001.png)
                try:
                    frame_num = int(basename.split('.')[0])
                    frame_numbers.append(frame_num)
                except:
                    frame_numbers.append(-1)
            
            print(f"  - Frame numbers: {frame_numbers}")
            
            # Check if it's actually a skip pattern (should be like [0, 2, 4] or [1, 3, 5])
            if len(frame_numbers) >= 3 and all(n >= 0 for n in frame_numbers):
                diff1 = frame_numbers[1] - frame_numbers[0]
                diff2 = frame_numbers[2] - frame_numbers[1]
                
                if diff1 == 2 and diff2 == 2:
                    print(f"  ✅ Correct skip pattern: gap of 2 frames")
                else:
                    print(f"  ⚠️  Unexpected pattern: gaps of {diff1}, {diff2}")
            else:
                print(f"  ❓ Could not verify frame pattern")

def main():
    """Main test function"""
    parser = argparse.ArgumentParser(description='Test skip sequences specifically')
    parser.add_argument('--dataset_path', default='./dataset/train/', type=str,
                       help='Path to dataset directory')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.dataset_path):
        print(f"❌ Dataset path does not exist: {args.dataset_path}")
        return
    
    test_skip_sequences(args.dataset_path)

if __name__ == "__main__":
    main()
