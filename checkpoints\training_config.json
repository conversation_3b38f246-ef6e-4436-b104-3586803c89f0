{"model": "SDCNet2DWithMasks", "dataset": "FrameLoaderWithMasks", "sequence_length": 2, "rgb_max": 255.0, "flownet2_checkpoint": "./flownet2_pytorch/FlowNet2_checkpoint.pth.tar", "epochs": 1000, "batch_size": 8, "val_batch_size": 2, "lr": 0.0001, "weight_decay": 0.0001, "workers": 4, "train_file": "./dataset/train/", "val_file": "./dataset/val/", "sample_rate": 1, "crop_size": [256, 320], "start_index": 0, "stride": 64, "skip_augmentation": true, "save_dir": "./checkpoints", "name": "sdcnet_with_masks", "resume": "", "patience": 10, "min_delta": 0.001, "gpu": 0, "fp16": false, "save_freq": 5, "inference_batches": [1, 1000, 2000], "inference_samples": 3, "keep_best_models": 3, "keep_regular_checkpoints": 2}