#!/usr/bin/env python3
"""
Automatic inference script for Original SDCNet (without masks).
This script automatically detects the best checkpoint and runs complete inference pipeline.
"""

import os
import sys
import argparse
import glob
import json
import subprocess
from datetime import datetime

# Add current directory to path
sys.path.append('.')


def find_best_checkpoint(checkpoint_dir="./checkpoints"):
    """Find the best checkpoint automatically"""
    if not os.path.exists(checkpoint_dir):
        return None

    # Look for best checkpoints first
    best_patterns = [
        "*original*best*.pth",
        "*sdcnet_original*best*.pth",
        "*original*.pth"
    ]

    for pattern in best_patterns:
        checkpoints = glob.glob(os.path.join(checkpoint_dir, "**", pattern), recursive=True)
        if checkpoints:
            # Sort by modification time (newest first)
            checkpoints.sort(key=os.path.getmtime, reverse=True)
            return checkpoints[0]

    # Fallback: look for any SDCNet original checkpoint
    fallback_patterns = [
        "*original*.pth",
        "*.pth"
    ]

    for pattern in fallback_patterns:
        checkpoints = glob.glob(os.path.join(checkpoint_dir, "**", pattern), recursive=True)
        if checkpoints:
            # Filter out mask-related checkpoints
            original_checkpoints = [cp for cp in checkpoints
                                  if 'mask' not in cp.lower() and 'original' in cp.lower()]
            if original_checkpoints:
                original_checkpoints.sort(key=os.path.getmtime, reverse=True)
                return original_checkpoints[0]

    return None


def get_checkpoint_info(checkpoint_path):
    """Get information about a checkpoint"""
    try:
        import torch
        checkpoint = torch.load(checkpoint_path, map_location='cpu')

        info = {
            'valid': True,
            'path': checkpoint_path,
            'filename': os.path.basename(checkpoint_path),
            'size_mb': os.path.getsize(checkpoint_path) / (1024 * 1024),
            'modified': datetime.fromtimestamp(os.path.getmtime(checkpoint_path)).isoformat()
        }

        # Extract training information
        if 'epoch' in checkpoint:
            info['epoch'] = checkpoint['epoch']

        if 'args' in checkpoint:
            training_args = checkpoint['args']
            info['training_config'] = {
                'batch_size': training_args.get('batch_size', 'unknown'),
                'lr': training_args.get('lr', 'unknown'),
                'skip_augmentation': training_args.get('skip_augmentation', False),
                'model': training_args.get('model', 'unknown')
            }

        if 'train_stats' in checkpoint:
            info['train_loss'] = checkpoint['train_stats'].get('total', 'unknown')

        if 'val_stats' in checkpoint:
            info['val_loss'] = checkpoint['val_stats'].get('total', 'unknown')

        return info

    except Exception as e:
        return {
            'valid': False,
            'error': str(e),
            'path': checkpoint_path
        }


def list_available_checkpoints(checkpoint_dir="./checkpoints"):
    """List all available checkpoints"""
    if not os.path.exists(checkpoint_dir):
        print(f"❌ Checkpoint directory not found: {checkpoint_dir}")
        return

    checkpoints = glob.glob(os.path.join(checkpoint_dir, "**", "*.pth"), recursive=True)

    if not checkpoints:
        print(f"❌ No checkpoints found in {checkpoint_dir}")
        return

    print(f"📋 Available checkpoints in {checkpoint_dir}:")
    print("-" * 80)

    for cp in sorted(checkpoints, key=os.path.getmtime, reverse=True):
        info = get_checkpoint_info(cp)
        if info['valid']:
            print(f"📄 {info['filename']}")
            print(f"   Path: {cp}")
            print(f"   Size: {info['size_mb']:.1f} MB")
            print(f"   Modified: {info['modified']}")
            if 'epoch' in info:
                print(f"   Epoch: {info['epoch']}")
            if 'training_config' in info:
                config = info['training_config']
                print(f"   Training: batch_size={config['batch_size']}, lr={config['lr']}")
                if config['skip_augmentation']:
                    print(f"   Skip Augmentation: ✅ Yes")
            print()


def run_inference(checkpoint_path, test_dir="./dataset/test", output_dir="./test_results", max_videos=None):
    """Run inference with the specified checkpoint"""

    print(f"🚀 Running inference...")
    print(f"   Checkpoint: {os.path.basename(checkpoint_path)}")
    print(f"   Test directory: {test_dir}")
    print(f"   Output directory: {output_dir}")

    # Build command
    cmd = [
        "python", "test_inference_original_sdcnet.py",
        "--checkpoint", checkpoint_path,
        "--test_dir", test_dir,
        "--output_dir", output_dir,
        "--batch_size", "8",  # Efficient batch size
        "--save_predictions",
        "--save_comparisons"
    ]

    # Add max_videos if specified
    if max_videos is not None:
        cmd.extend(["--max_videos", str(max_videos)])

    print(f"\n💻 Executing: {' '.join(cmd)}")
    print("-" * 60)

    # Run inference
    result = subprocess.run(cmd, capture_output=False)

    return result.returncode == 0


def run_video_creation(inference_dir, test_dir="./dataset/test", fps=12):
    """Create comparison videos from inference results"""

    print(f"\n🎬 Creating comparison videos...")

    # Build command
    cmd = [
        "python", "create_comparison_videos_original.py",
        "--inference_dir", inference_dir,
        "--test_dir", test_dir,
        "--fps", str(fps),
        "--quality", "medium"
    ]

    print(f"💻 Executing: {' '.join(cmd)}")
    print("-" * 60)

    # Run video creation
    result = subprocess.run(cmd, capture_output=False)

    return result.returncode == 0


def main():
    """Main function"""

    parser = argparse.ArgumentParser(description='Automatic Inference for Original SDCNet')
    parser.add_argument('--checkpoint', default=None, type=str,
                       help='Path to checkpoint (auto-detect if not provided)')
    parser.add_argument('--test_dir', default='./dataset/test', type=str,
                       help='Path to test dataset')
    parser.add_argument('--output_dir', default='./test_results', type=str,
                       help='Output directory for results')
    parser.add_argument('--list_checkpoints', action='store_true',
                       help='List available checkpoints and exit')
    parser.add_argument('--skip_videos', action='store_true',
                       help='Skip video creation (inference only)')
    parser.add_argument('--fps', type=int, default=12,
                       help='Video frame rate (default: 12)')
    parser.add_argument('--max_videos', type=int, default=None,
                       help='Limit number of videos for testing')

    args = parser.parse_args()

    print("🚀 Original SDCNet - Automatic Inference")
    print("=" * 60)

    # List checkpoints if requested
    if args.list_checkpoints:
        list_available_checkpoints()
        return

    # Find checkpoint
    if args.checkpoint is None:
        print("🔍 Auto-detecting best checkpoint...")
        args.checkpoint = find_best_checkpoint()

        if args.checkpoint is None:
            print("❌ No checkpoint found in ./checkpoints/")
            print("   Please train a model first or specify --checkpoint")
            print("   Use --list_checkpoints to see available checkpoints")
            return

    # Validate checkpoint
    if not os.path.exists(args.checkpoint):
        print(f"❌ Checkpoint not found: {args.checkpoint}")
        return

    # Get checkpoint info
    info = get_checkpoint_info(args.checkpoint)

    if not info or not info['valid']:
        print(f"❌ Invalid checkpoint: {args.checkpoint}")
        if info and 'error' in info:
            print(f"   Error: {info['error']}")
        return

    # Display checkpoint information
    print(f"\n📊 Checkpoint Information:")
    print(f"   File: {info['filename']}")
    if 'epoch' in info:
        print(f"   Epoch: {info['epoch']}")
    print(f"   Size: {info['size_mb']:.1f} MB")
    if 'training_config' in info:
        config = info['training_config']
        print(f"   Model: {config['model']}")
        if config['skip_augmentation']:
            print(f"   Skip Augmentation: ✅ Yes")
            print(f"   📝 Note: Model was trained with skip augmentation")
            print(f"           Inference will use consecutive sequences (standard)")
        else:
            print(f"   Skip Augmentation: ❌ No")

    # Validate test directory
    if not os.path.exists(args.test_dir):
        print(f"❌ Test directory not found: {args.test_dir}")
        return

    # Check if test directory has the right structure
    x_dir = os.path.join(args.test_dir, 'X')
    y_dir = os.path.join(args.test_dir, 'Y')

    if not os.path.exists(x_dir):
        print(f"❌ Test directory must contain X/ subdirectory")
        return

    print(f"✅ Test directory validated")

    # Run inference
    print(f"\n" + "=" * 50)
    print("STEP 1: RUNNING INFERENCE")
    print("=" * 50)

    success = run_inference(args.checkpoint, args.test_dir, args.output_dir, args.max_videos)

    if not success:
        print("❌ Inference failed!")
        return

    print("✅ Inference completed successfully!")

    # Find the inference output directory
    inference_dirs = glob.glob(os.path.join(args.output_dir, "inference_*"))
    if not inference_dirs:
        print("❌ Could not find inference output directory")
        return

    # Use the most recent inference directory
    inference_dir = max(inference_dirs, key=os.path.getmtime)
    print(f"📁 Inference results: {inference_dir}")

    # Create videos (unless skipped)
    if not args.skip_videos:
        print(f"\n" + "=" * 50)
        print("STEP 2: CREATING COMPARISON VIDEOS")
        print("=" * 50)

        video_success = run_video_creation(inference_dir, args.test_dir, args.fps)

        if video_success:
            print("✅ Video creation completed successfully!")
            video_dir = os.path.join(inference_dir, 'videos')
            print(f"🎬 Videos saved to: {video_dir}")
        else:
            print("❌ Video creation failed!")

    # Final summary
    print(f"\n" + "=" * 60)
    print("🎉 INFERENCE PIPELINE COMPLETED")
    print("=" * 60)
    print(f"📁 Results directory: {inference_dir}")
    if not args.skip_videos:
        print(f"🎬 Videos directory: {os.path.join(inference_dir, 'videos')}")
    print(f"\n💡 Next steps:")
    print(f"   1. Check inference results in: {inference_dir}")
    if not args.skip_videos:
        print(f"   2. Watch comparison videos in: {os.path.join(inference_dir, 'videos')}")
    print(f"   3. Compare with SDCNet with masks results")


if __name__ == "__main__":
    main()
