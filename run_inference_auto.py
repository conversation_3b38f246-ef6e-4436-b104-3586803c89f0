#!/usr/bin/env python3
"""
Automatic inference script that works with both regular and skip-augmentation trained models.
This script automatically detects the training configuration and runs inference accordingly.
"""

import os
import sys
import argparse
import glob
from datetime import datetime

# Add current directory to path
sys.path.append('.')

def find_best_checkpoint(checkpoint_dir="./checkpoints"):
    """Find the best available checkpoint"""
    
    if not os.path.exists(checkpoint_dir):
        return None
    
    # Priority order for checkpoint selection
    patterns = [
        "*best*.pth",           # Best checkpoints first
        "*best*.pth.tar",
        "*.pth",               # Any .pth files
        "*.pth.tar"            # Any .pth.tar files
    ]
    
    best_checkpoint = None
    best_time = 0
    
    for pattern in patterns:
        checkpoints = glob.glob(os.path.join(checkpoint_dir, pattern))
        for ckpt in checkpoints:
            mtime = os.path.getmtime(ckpt)
            if mtime > best_time:
                best_time = mtime
                best_checkpoint = ckpt
        
        # If we found a best checkpoint, use it
        if best_checkpoint and "best" in pattern:
            break
    
    return best_checkpoint

def get_checkpoint_info(checkpoint_path):
    """Get information about a checkpoint"""
    
    if not os.path.exists(checkpoint_path):
        return None
    
    try:
        import torch
        checkpoint = torch.load(checkpoint_path, map_location='cpu')
        
        info = {
            'path': checkpoint_path,
            'epoch': checkpoint.get('epoch', 'unknown'),
            'model_type': 'unknown',
            'skip_augmentation': False,
            'valid': True
        }
        
        if 'args' in checkpoint:
            training_args = checkpoint['args']
            info['model_type'] = training_args.get('model', 'unknown')
            info['skip_augmentation'] = training_args.get('skip_augmentation', False)
        
        return info
        
    except Exception as e:
        return {
            'path': checkpoint_path,
            'valid': False,
            'error': str(e)
        }

def run_inference(checkpoint_path, test_dir="./dataset/test", output_dir="./test_results"):
    """Run inference with the specified checkpoint"""
    
    print(f"🚀 Running inference...")
    print(f"Checkpoint: {os.path.basename(checkpoint_path)}")
    print(f"Test directory: {test_dir}")
    print(f"Output directory: {output_dir}")
    
    # Build command
    cmd = [
        "python", "test_inference.py",
        "--checkpoint", checkpoint_path,
        "--test_dir", test_dir,
        "--output_dir", output_dir
    ]
    
    print(f"\nExecuting: {' '.join(cmd)}")
    print("-" * 60)
    
    # Run inference
    import subprocess
    result = subprocess.run(cmd, capture_output=False)
    
    return result.returncode == 0

def main():
    """Main function"""
    
    parser = argparse.ArgumentParser(description='Automatic Inference for SDCNet')
    parser.add_argument('--checkpoint', default=None, type=str,
                       help='Path to checkpoint (auto-detect if not provided)')
    parser.add_argument('--test_dir', default='./dataset/test', type=str,
                       help='Path to test dataset')
    parser.add_argument('--output_dir', default='./test_results', type=str,
                       help='Output directory for results')
    parser.add_argument('--list_checkpoints', action='store_true',
                       help='List available checkpoints and exit')
    
    args = parser.parse_args()
    
    print("🔍 SDCNet Automatic Inference")
    print("=" * 50)
    
    # List checkpoints if requested
    if args.list_checkpoints:
        print("Available checkpoints:")
        checkpoints = glob.glob("./checkpoints/*.pth") + glob.glob("./checkpoints/*.pth.tar")
        
        if not checkpoints:
            print("  No checkpoints found in ./checkpoints/")
            return
        
        for ckpt in sorted(checkpoints):
            info = get_checkpoint_info(ckpt)
            if info and info['valid']:
                skip_info = "✅ Skip Aug" if info['skip_augmentation'] else "   Regular"
                print(f"  {skip_info} | Epoch {info['epoch']:>3} | {os.path.basename(ckpt)}")
            else:
                print(f"  ❌ Invalid | {os.path.basename(ckpt)}")
        return
    
    # Find checkpoint
    if args.checkpoint is None:
        print("🔍 Auto-detecting best checkpoint...")
        args.checkpoint = find_best_checkpoint()
        
        if args.checkpoint is None:
            print("❌ No checkpoint found in ./checkpoints/")
            print("   Please train a model first or specify --checkpoint")
            return
    
    # Validate checkpoint
    if not os.path.exists(args.checkpoint):
        print(f"❌ Checkpoint not found: {args.checkpoint}")
        return
    
    # Get checkpoint info
    info = get_checkpoint_info(args.checkpoint)
    
    if not info or not info['valid']:
        print(f"❌ Invalid checkpoint: {args.checkpoint}")
        if info and 'error' in info:
            print(f"   Error: {info['error']}")
        return
    
    # Display checkpoint info
    print(f"📊 Checkpoint Information:")
    print(f"   File: {os.path.basename(info['path'])}")
    print(f"   Epoch: {info['epoch']}")
    print(f"   Model: {info['model_type']}")
    print(f"   Skip Augmentation: {'✅ Yes' if info['skip_augmentation'] else '❌ No'}")
    
    if info['skip_augmentation']:
        print(f"   📝 Note: Model was trained with skip augmentation")
        print(f"           Inference will use consecutive sequences (standard)")
    
    # Validate test directory
    if not os.path.exists(args.test_dir):
        print(f"❌ Test directory not found: {args.test_dir}")
        return
    
    # Check if test directory has the right structure
    x_dir = os.path.join(args.test_dir, 'X')
    y_dir = os.path.join(args.test_dir, 'Y')
    
    if not os.path.exists(x_dir) or not os.path.exists(y_dir):
        print(f"❌ Test directory must contain X/ and Y/ subdirectories")
        return
    
    print(f"✅ Test directory validated")
    
    # Run inference
    print(f"\n" + "=" * 50)
    success = run_inference(args.checkpoint, args.test_dir, args.output_dir)
    
    if success:
        print(f"\n🎉 Inference completed successfully!")
        print(f"Results saved to: {args.output_dir}")
        
        # Look for the latest inference results
        inference_dirs = glob.glob(os.path.join(args.output_dir, "inference_*"))
        if inference_dirs:
            latest_dir = max(inference_dirs, key=os.path.getmtime)
            print(f"Latest results: {latest_dir}")
            
            # Check for video creation script
            if os.path.exists("create_comparison_videos.py"):
                print(f"\n💡 To create comparison videos, run:")
                print(f"python create_comparison_videos.py --results_dir {latest_dir}")
    else:
        print(f"\n❌ Inference failed!")
        print(f"Check the error messages above for details")

if __name__ == "__main__":
    main()
