from __future__ import division
from __future__ import print_function

import os
import natsort
import numpy as np
import cv2

import torch
from torch.utils import data
from datasets.dataset_utils import StaticRandomCrop

import glob
import pdb

class FrameLoaderWithMasks(data.Dataset):
    def __init__(self, args, root, is_training = False, transform=None):

        self.is_training = is_training
        self.transform = transform
        self.chsize = 3

        # carry over command line arguments
        assert args.sequence_length > 1, 'sequence length must be > 1'
        self.sequence_length = args.sequence_length

        assert args.sample_rate > 0, 'sample rate must be > 0'
        self.sample_rate = args.sample_rate

        self.crop_size = args.crop_size
        self.start_index = args.start_index
        self.stride = args.stride

        # Skip augmentation for data augmentation (t-2,t -> t+2)
        self.skip_augmentation = getattr(args, 'skip_augmentation', False) and is_training
        print(f"Skip augmentation: {'enabled' if self.skip_augmentation else 'disabled'}")

        print("root path provided: {}".format(root))
        assert (os.path.exists(root))
        if self.is_training:
            self.start_index = 0

        # collect frame and mask file lists
        self.ref = self.collect_filelist(root)

        # Calculate counts for both consecutive and skip sequences
        consecutive_counts = [((len(el['frames']) - self.sequence_length) // (self.sample_rate)) for el in self.ref]

        if self.skip_augmentation:
            # For skip sequences, we need at least sequence_length + 3 frames
            # (e.g., for t-2,t,t+2 with sequence_length=2 we need frames 0,1,2,3,4 = 5 frames minimum)
            # But we also need to account for the fact that we're skipping frames
            skip_counts = [max(0, ((len(el['frames']) - 4) // (self.sample_rate))) for el in self.ref]
            # Total is sum of both consecutive and skip sequences
            total_consecutive = np.sum(consecutive_counts)
            total_skip = np.sum(skip_counts)
            self.total = total_consecutive + total_skip
            print(f"Dataset size: {total_consecutive} consecutive + {total_skip} skip = {self.total} total sequences")
        else:
            self.total = np.sum(consecutive_counts)
            print(f"Dataset size: {self.total} consecutive sequences")

        # Store counts for indexing
        self.consecutive_counts = consecutive_counts
        self.consecutive_total = np.sum(consecutive_counts)
        self.consecutive_cum_sum = list(np.cumsum([0] + consecutive_counts))

        if self.skip_augmentation:
            self.skip_counts = skip_counts
            self.skip_total = np.sum(skip_counts)
            self.skip_cum_sum = list(np.cumsum([0] + skip_counts))

    def collect_filelist(self, root):
        """
        Collect frame and mask file lists from the dataset structure.
        Expected structure:
        root/
        ├── X/
        │   ├── video_name/           # Frame directory
        │   ├── video_name_binary_ground_truth/  # Mask directory
        └── Y/
            ├── video_name/           # Target frame directory
        """
        datasets = []

        # Look for X and Y directories
        x_dir = os.path.join(root, 'X')
        y_dir = os.path.join(root, 'Y')

        if not os.path.exists(x_dir) or not os.path.exists(y_dir):
            raise ValueError(f"Expected X and Y directories in {root}")

        # Get all video directories in X (excluding mask directories)
        video_dirs = []
        for item in os.listdir(x_dir):
            item_path = os.path.join(x_dir, item)
            if os.path.isdir(item_path) and not item.endswith('_binary_ground_truth'):
                video_dirs.append(item)

        video_dirs = natsort.natsorted(video_dirs)

        for video_name in video_dirs:
            # Frame directories
            frame_dir = os.path.join(x_dir, video_name)
            mask_dir = os.path.join(x_dir, video_name + '_binary_ground_truth')
            target_dir = os.path.join(y_dir, video_name)

            # Check if all directories exist
            if not all(os.path.exists(d) for d in [frame_dir, mask_dir, target_dir]):
                print(f"Warning: Skipping {video_name} - missing directories")
                continue

            # Collect frame files
            frame_files = natsort.natsorted(glob.glob(os.path.join(frame_dir, "*.png")))
            mask_files = natsort.natsorted(glob.glob(os.path.join(mask_dir, "*.png")))
            target_files = natsort.natsorted(glob.glob(os.path.join(target_dir, "*.png")))

            # Verify file counts match
            if len(frame_files) != len(mask_files) or len(frame_files) != len(target_files):
                print(f"Warning: Skipping {video_name} - file count mismatch")
                print(f"  Frames: {len(frame_files)}, Masks: {len(mask_files)}, Targets: {len(target_files)}")
                continue

            if len(frame_files) < self.sequence_length + 1:
                print(f"Warning: Skipping {video_name} - insufficient frames ({len(frame_files)} < {self.sequence_length + 1})")
                continue

            datasets.append({
                'frames': frame_files,
                'masks': mask_files,
                'targets': target_files,
                'video_name': video_name
            })

        print(f"Found {len(datasets)} valid video sequences")
        return datasets

    def __len__(self):
        return self.total

    def __getitem__(self, index):
        # adjust index
        index = len(self) + index if index < 0 else index
        index = index + self.start_index

        # Determine if this is a consecutive or skip sequence
        if self.skip_augmentation and index >= self.consecutive_total:
            # This is a skip sequence (t-2, t, t+2)
            skip_index = index - self.consecutive_total
            dataset_index = np.searchsorted(self.skip_cum_sum, skip_index + 1)
            local_index = self.sample_rate * (skip_index - self.skip_cum_sum[np.maximum(0, dataset_index - 1)])

            video_data = self.ref[dataset_index - 1]

            # For skip sequences: we use frames at t-2, t, t+2 and masks at t-2, t, t+2
            # We need to ensure we have enough frames (at least sequence_length + 2)
            # The local_index is already adjusted for the available skip sequences
            frame_indices = [local_index, local_index + 2, local_index + 4]  # t-2, t, t+2
            mask_indices = [local_index, local_index + 2, local_index + 4]   # t-2, t, t+2
            target_indices = [local_index, local_index + 2, local_index + 4] # t-2, t, t+2

            frame_files = [video_data['frames'][idx] for idx in frame_indices]
            mask_files = [video_data['masks'][idx] for idx in mask_indices]
            target_files = [video_data['targets'][idx] for idx in target_indices]

            sequence_type = "skip"
        else:
            # This is a consecutive sequence (t-1, t, t+1)
            consecutive_index = index if not self.skip_augmentation else index
            dataset_index = np.searchsorted(self.consecutive_cum_sum, consecutive_index + 1)
            local_index = self.sample_rate * (consecutive_index - self.consecutive_cum_sum[np.maximum(0, dataset_index - 1)])

            video_data = self.ref[dataset_index - 1]

            # Get file paths for sequence_length + 1 frames (for t-1, t, t+1)
            frame_files = [video_data['frames'][local_index + offset] for offset in range(self.sequence_length + 1)]
            mask_files = [video_data['masks'][local_index + offset] for offset in range(self.sequence_length + 1)]
            target_files = [video_data['targets'][local_index + offset] for offset in range(self.sequence_length + 1)]

            sequence_type = "consecutive"

        # reverse image order with p=0.5 (DISABLED for clearer visualization)
        # if self.is_training and torch.randint(0, 2, (1,)).item():
        #     frame_files = frame_files[::-1]
        #     mask_files = mask_files[::-1]
        #     target_files = target_files[::-1]

        # Load frames (RGB)
        frames = [cv2.imread(frame_file)[..., :self.chsize] for frame_file in frame_files]

        # Load masks (grayscale, convert to single channel)
        masks = []
        for mask_file in mask_files:
            mask = cv2.imread(mask_file, cv2.IMREAD_GRAYSCALE)
            # Ensure binary values (0 or 255)
            mask = np.where(mask > 127, 255, 0).astype(np.uint8)
            masks.append(mask)

        # Pad images and masks along height and width to fit them evenly into models.
        input_shape = frames[0].shape[:2]
        height, width = input_shape

        if (height % self.stride) != 0:
            padded_height = (height // self.stride + 1) * self.stride
            frames = [np.pad(im, ((0, padded_height - height), (0,0), (0,0)), 'reflect') for im in frames]
            masks = [np.pad(mask, ((0, padded_height - height), (0,0)), 'reflect') for mask in masks]

        if (width % self.stride) != 0:
            padded_width = (width // self.stride + 1) * self.stride
            frames = [np.pad(im, ((0, 0), (0, padded_width - width), (0, 0)), 'reflect') for im in frames]
            masks = [np.pad(mask, ((0, 0), (0, padded_width - width)), 'reflect') for mask in masks]

        # StaticRandomCrop placed after the padding
        if self.is_training:
            cropper = StaticRandomCrop(self.crop_size, input_shape)
            frames = list(map(cropper, frames))
            # Apply same crop to masks (add channel dimension temporarily)
            masks_with_channel = [mask[..., np.newaxis] for mask in masks]
            masks_with_channel = list(map(cropper, masks_with_channel))
            masks = [mask[..., 0] for mask in masks_with_channel]

        # Convert to tensors
        input_frames = [torch.from_numpy(frame.transpose(2, 0, 1)).float() for frame in frames]
        input_masks = [torch.from_numpy(mask).float().unsqueeze(0) for mask in masks]  # Add channel dimension

        output_dict = {
            'image': input_frames,
            'mask': input_masks,
            'ishape': input_shape,
            'input_files': frame_files,
            'mask_files': mask_files,
            'target_files': target_files,
            'video_name': video_data['video_name'],
            'sequence_type': sequence_type
        }

        return output_dict
