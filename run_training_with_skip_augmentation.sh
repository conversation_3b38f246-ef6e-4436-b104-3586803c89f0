#!/bin/bash

# SDCNet Training with Skip Augmentation
# This script demonstrates how to run training with the new skip frame augmentation feature

echo "🚀 Starting SDCNet Training with Skip Augmentation"
echo "=================================================="

# Check if dataset exists
if [ ! -d "./dataset/train/" ]; then
    echo "❌ Training dataset not found at ./dataset/train/"
    echo "Please ensure your dataset is properly structured."
    exit 1
fi

if [ ! -d "./dataset/val/" ]; then
    echo "❌ Validation dataset not found at ./dataset/val/"
    echo "Please ensure your dataset is properly structured."
    exit 1
fi

# Check if FlowNet2 checkpoint exists
if [ ! -f "./flownet2_pytorch/FlowNet2_checkpoint.pth.tar" ]; then
    echo "❌ FlowNet2 checkpoint not found"
    echo "Please download the FlowNet2 checkpoint first."
    exit 1
fi

echo "✅ All prerequisites found"
echo ""

# Create checkpoints directory
mkdir -p ./checkpoints

# Training command with skip augmentation
echo "🎯 Training Configuration:"
echo "- Skip augmentation: ENABLED"
echo "- Dataset size will be approximately doubled"
echo "- Training includes both consecutive (t-1,t→t+1) and skip (t-2,t→t+2) sequences"
echo ""

python train_sdcnet_with_masks.py \
    --skip_augmentation \
    --model SDCNet2DWithMasks \
    --dataset FrameLoaderWithMasks \
    --train_file ./dataset/train/ \
    --val_file ./dataset/val/ \
    --epochs 100 \
    --batch_size 4 \
    --val_batch_size 2 \
    --lr 0.0001 \
    --weight_decay 1e-4 \
    --workers 4 \
    --crop_size 256 320 \
    --sample_rate 1 \
    --stride 64 \
    --save_dir ./checkpoints \
    --name sdcnet_with_skip_augmentation \
    --patience 10 \
    --min_delta 0.001 \
    --save_freq 5 \
    --inference_batches 1 1000 2000 \
    --inference_samples 3 \
    --gpu 0

echo ""
echo "🎉 Training completed!"
echo "Check ./checkpoints/sdcnet_with_skip_augmentation/ for results"
