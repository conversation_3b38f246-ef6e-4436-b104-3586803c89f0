#!/usr/bin/env python3
"""
Simple HTTP server for SDCNet Comparison Viewer
Serves static files and provides API endpoints for video/inference data
"""

import os
import json
import glob
from pathlib import Path
from http.server import HTTPServer, SimpleHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import mimetypes

class SDCNetViewerHandler(SimpleHTTPRequestHandler):
    """Custom handler for the SDCNet viewer with API endpoints"""

    def __init__(self, *args, **kwargs):
        # Load configuration
        self.config = self.load_config()
        super().__init__(*args, **kwargs)

    def load_config(self):
        """Load configuration from JSON file"""
        try:
            with open('viewer_config.json', 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            return self.get_default_config()

    def get_default_config(self):
        """Return default configuration if config file not found"""
        return {
            "paths": {
                "dataset_test_x": "dataset/test/X",
                "dataset_test_y": "dataset/test/Y",
                "test_results": "test_results"
            },
            "default_settings": {
                "frame_range": {"start": 2, "end": 89}
            }
        }

    def do_GET(self):
        """Handle GET requests"""
        parsed_path = urlparse(self.path)

        # API endpoints
        if parsed_path.path.startswith('/api/'):
            self.handle_api_request(parsed_path)
        else:
            # Serve static files
            super().do_GET()

    def handle_api_request(self, parsed_path):
        """Handle API requests"""
        path_parts = parsed_path.path.split('/')

        try:
            if len(path_parts) >= 3:
                endpoint = path_parts[2]

                if endpoint == 'videos':
                    self.get_videos()
                elif endpoint == 'inference':
                    self.get_inference_results()
                elif endpoint == 'frames':
                    query_params = parse_qs(parsed_path.query)
                    video = query_params.get('video', [None])[0]
                    inference = query_params.get('inference', [None])[0]
                    self.get_available_frames(video, inference)
                elif endpoint == 'sources':
                    query_params = parse_qs(parsed_path.query)
                    video = query_params.get('video', [None])[0]
                    self.get_available_sources(video)
                elif endpoint == 'check-image':
                    query_params = parse_qs(parsed_path.query)
                    path = query_params.get('path', [None])[0]
                    self.check_image_exists(path)
                else:
                    self.send_api_error(404, "Endpoint not found")
            else:
                self.send_api_error(400, "Invalid API path")

        except Exception as e:
            self.send_api_error(500, f"Server error: {str(e)}")

    def get_videos(self):
        """Get list of available videos"""
        try:
            dataset_path = self.config['paths']['dataset_test_y']

            if os.path.exists(dataset_path):
                videos = []
                for item in os.listdir(dataset_path):
                    item_path = os.path.join(dataset_path, item)
                    if os.path.isdir(item_path):
                        videos.append(item)
                videos.sort()
            else:
                # Fallback to config list
                videos = self.config.get('video_list', [])

            self.send_api_response({'videos': videos})

        except Exception as e:
            self.send_api_error(500, f"Error getting videos: {str(e)}")

    def get_inference_results(self):
        """Get list of available inference results"""
        try:
            test_results_path = self.config['paths']['test_results']

            inference_results = []

            if os.path.exists(test_results_path):
                for item in os.listdir(test_results_path):
                    item_path = os.path.join(test_results_path, item)
                    if os.path.isdir(item_path) and item.startswith('inference_'):
                        # Extract timestamp and format it
                        timestamp = item.replace('inference_', '').replace('_', '-')
                        inference_results.append({
                            'id': item,
                            'display_name': timestamp,
                            'description': f'Inference results from {timestamp}'
                        })

                # Sort by timestamp (newest first)
                inference_results.sort(key=lambda x: x['id'], reverse=True)
            else:
                # Fallback to config list
                inference_results = self.config.get('inference_results', [])

            self.send_api_response({'inference_results': inference_results})

        except Exception as e:
            self.send_api_error(500, f"Error getting inference results: {str(e)}")

    def get_available_frames(self, video, inference):
        """Get available frames for a video/inference combination"""
        try:
            if not video or not inference:
                self.send_api_error(400, "Video and inference parameters required")
                return

            # Check ground truth frames
            gt_path = os.path.join(self.config['paths']['dataset_test_y'], video)
            available_frames = []
            frame_format = None  # Will be 5 or 6 digits

            if os.path.exists(gt_path):
                frame_files = glob.glob(os.path.join(gt_path, "*.png"))
                for frame_file in frame_files:
                    frame_name = os.path.basename(frame_file)
                    frame_number_str = frame_name.split('.')[0]
                    frame_number = int(frame_number_str)
                    available_frames.append(frame_number)

                    # Detect frame format (5 or 6 digits)
                    if frame_format is None:
                        frame_format = len(frame_number_str)

                available_frames.sort()

            # Filter to typical prediction range (frames 2-89)
            frame_range = self.config['default_settings']['frame_range']
            filtered_frames = [f for f in available_frames
                             if frame_range['start'] <= f <= frame_range['end']]

            self.send_api_response({
                'frames': filtered_frames,
                'total': len(filtered_frames),
                'range': frame_range,
                'frame_format': frame_format or 5  # Default to 5 digits
            })

        except Exception as e:
            self.send_api_error(500, f"Error getting frames: {str(e)}")

    def get_available_sources(self, video):
        """Get available sources for a video (ground truth + inference results)"""
        try:
            if not video:
                self.send_api_error(400, "Video parameter required")
                return

            sources = []

            # Add ground truth source
            gt_path = os.path.join(self.config['paths']['dataset_test_y'], video)
            if os.path.exists(gt_path):
                sources.append({
                    'id': 'ground_truth',
                    'name': 'Ground Truth',
                    'type': 'ground_truth',
                    'path_template': f"dataset/test/Y/{video}/{{frame}}.png",
                    'description': 'Original target frames from the dataset'
                })

            # Add inference results sources
            test_results_path = self.config['paths']['test_results']
            if os.path.exists(test_results_path):
                # Get all inference directories and sort them
                inference_dirs = []
                for item in os.listdir(test_results_path):
                    item_path = os.path.join(test_results_path, item)
                    if os.path.isdir(item_path) and item.startswith('inference_'):
                        # Check if this inference has results for the video
                        video_path = os.path.join(item_path, video)
                        if os.path.exists(video_path):
                            inference_dirs.append(item)

                # Sort by timestamp (newest first)
                inference_dirs.sort(reverse=True)

                for item in inference_dirs:
                    # Determine the type and naming convention
                    display_name = item.replace('inference_', '').replace('_', '-')

                    # Check if files use 'pred_' prefix
                    video_path = os.path.join(test_results_path, item, video)
                    sample_files = os.listdir(video_path)[:5]  # Check first 5 files
                    uses_pred_prefix = any(f.startswith('pred_') for f in sample_files if f.endswith('.png'))

                    if uses_pred_prefix:
                        path_template = f"test_results/{item}/{video}/pred_{{frame}}.png"
                        file_prefix = "pred_"
                    else:
                        path_template = f"test_results/{item}/{video}/{{frame}}.png"
                        file_prefix = ""

                    # Determine model type from directory name
                    if '_masked' in item:
                        if '_skip' in item:
                            model_type = "Masked + Skip"
                            source_type = 'masked_skip'
                        else:
                            model_type = "Masked"
                            source_type = 'masked_no_skip'
                    else:
                        if '_skip' in item:
                            model_type = "Original + Skip"
                            source_type = 'original_skip'
                        else:
                            model_type = "Original"
                            source_type = 'original_no_skip'

                    sources.append({
                        'id': f"{item}_{video}",
                        'name': f"SDCNet {model_type} ({display_name})",
                        'type': source_type,
                        'inference_dir': item,
                        'path_template': path_template,
                        'file_prefix': file_prefix,
                        'timestamp': display_name,
                        'description': f'SDCNet {model_type} - Results from {display_name}'
                    })

            self.send_api_response({
                'sources': sources,
                'video': video,
                'total': len(sources)
            })

        except Exception as e:
            self.send_api_error(500, f"Error getting sources: {str(e)}")

    def check_image_exists(self, image_path):
        """Check if an image file exists"""
        try:
            if not image_path:
                self.send_api_error(400, "Path parameter required")
                return

            exists = os.path.exists(image_path)

            response = {
                'exists': exists,
                'path': image_path
            }

            if exists:
                stat = os.stat(image_path)
                response['size'] = stat.st_size
                response['modified'] = stat.st_mtime

            self.send_api_response(response)

        except Exception as e:
            self.send_api_error(500, f"Error checking image: {str(e)}")

    def send_api_response(self, data):
        """Send JSON API response"""
        response_data = json.dumps(data, indent=2)

        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Content-Length', str(len(response_data)))
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()

        self.wfile.write(response_data.encode('utf-8'))

    def send_api_error(self, status_code, message):
        """Send JSON error response"""
        error_data = json.dumps({
            'error': True,
            'message': message,
            'status': status_code
        }, indent=2)

        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Content-Length', str(len(error_data)))
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()

        self.wfile.write(error_data.encode('utf-8'))

    def end_headers(self):
        """Add CORS headers"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()

def run_server(port=8000):
    """Run the HTTP server"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, SDCNetViewerHandler)

    print(f"🚀 SDCNet Comparison Viewer Server")
    print(f"📡 Server running on http://localhost:{port}")
    print(f"🎬 Open http://localhost:{port}/sdcnet_comparison_viewer.html to view")
    print(f"⚡ API endpoints available at http://localhost:{port}/api/")
    print(f"🛑 Press Ctrl+C to stop the server")

    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
        httpd.server_close()

if __name__ == '__main__':
    import sys

    port = 8000
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print("Invalid port number. Using default port 8000.")

    run_server(port)
