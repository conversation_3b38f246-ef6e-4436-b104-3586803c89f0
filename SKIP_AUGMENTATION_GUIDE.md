# Skip Frame Augmentation Guide

## 🎯 Overview

Skip frame augmentation is a new data augmentation technique that enhances SDCNet's ability to handle fast motion scenarios by training on both consecutive and skip frame sequences.

### What it does:
- **Consecutive sequences**: `t-1, t → t+1` (original behavior)
- **Skip sequences**: `t-2, t → t+2` (new augmentation)

This effectively doubles the training dataset size and improves performance on videos with rapid movements.

## 🚀 Quick Start

### 1. Enable Skip Augmentation
Add the `--skip_augmentation` flag to your training command:

```bash
python train_sdcnet_with_masks.py \
    --skip_augmentation \
    --train_file ./dataset/train/ \
    --val_file ./dataset/val/ \
    --epochs 100 \
    --batch_size 4
```

### 2. Test Before Training
Verify everything works correctly:

```bash
python test_skip_augmentation.py --dataset_path ./dataset/train/
```

### 3. Run Example Training
Use the provided example script:

```bash
python train_with_skip_augmentation_example.py
```

## 📊 Technical Details

### Dataset Size Impact
- **Without skip augmentation**: N sequences
- **With skip augmentation**: ~2N sequences (consecutive + skip)

### Memory Requirements
- Videos need **at least 5 frames** for skip sequences
- Shorter videos will only contribute consecutive sequences
- Memory usage increases proportionally to dataset size

### Training Behavior
- **Training set**: Uses both consecutive and skip sequences
- **Validation set**: Uses consecutive sequences only (for consistency)
- **Sequence type tracking**: Each sample includes `sequence_type` metadata

## 🔧 Implementation Details

### Frame Selection Logic

#### Consecutive Sequences (Original)
```
Frames: [0, 1, 2, 3, 4, 5, ...]
Input:  [t-1, t] = [0, 1]
Target: [t+1] = [2]
Masks:  [t-1, t, t+1] = [0, 1, 2]
```

#### Skip Sequences (New)
```
Frames: [0, 1, 2, 3, 4, 5, ...]
Input:  [t-2, t] = [0, 2]
Target: [t+2] = [4]
Masks:  [t-2, t, t+2] = [0, 2, 4]
```

### Edge Case Handling
- Videos with < 5 frames: Skip sequences disabled, consecutive only
- Automatic detection of available frames per video
- Graceful fallback to consecutive-only mode

## 📈 Expected Benefits

### Performance Improvements
- **Fast motion handling**: Better prediction of rapid movements
- **Temporal robustness**: Learns longer-term dependencies
- **Generalization**: More diverse training scenarios

### Training Characteristics
- **Longer training time**: ~2x due to dataset size increase
- **Better convergence**: More training data typically improves results
- **Memory efficiency**: No additional memory per sample, just more samples

## 🧪 Testing and Validation

### Test Script Features
The `test_skip_augmentation.py` script verifies:
- ✅ Dataset loading with/without skip augmentation
- ✅ Correct sequence type assignment
- ✅ Frame indexing accuracy
- ✅ DataLoader compatibility
- ✅ Edge case handling

### Sample Output
```
Testing dataset: ./dataset/train/
Skip augmentation: enabled
Dataset size: 1000 consecutive + 800 skip = 1800 total sequences

Sample 0: consecutive sequence from video_001
  - Frames: 3, Masks: 3
  - Frame files: ['000001.png', '000002.png', '000003.png']

Sample 1000: skip sequence from video_001
  - Frames: 3, Masks: 3
  - Frame files: ['000001.png', '000003.png', '000005.png']
```

## ⚙️ Configuration Options

### Command Line Arguments
```bash
--skip_augmentation     # Enable skip frame augmentation (default: False)
--batch_size 4          # May need reduction due to larger dataset
--workers 4             # Parallel data loading workers
--patience 10           # Early stopping patience
```

### Recommended Settings
```bash
# For fast training (smaller dataset)
--skip_augmentation --batch_size 6 --workers 2

# For best quality (larger dataset)
--skip_augmentation --batch_size 4 --workers 4 --epochs 150
```

## 🔍 Monitoring and Debugging

### Training Logs
Look for these indicators:
```
Skip augmentation: enabled
Dataset size: 1000 consecutive + 800 skip = 1800 total sequences
Training samples: 1800
```

### Inference Samples
Saved samples include sequence type information:
```json
{
  "total_loss": 0.234,
  "sequence_type": "skip",
  "video_name": "kitchen_scene_01"
}
```

## 🚨 Troubleshooting

### Common Issues

#### 1. Dataset Too Small
```
Error: No skip sequences available
Solution: Ensure videos have ≥5 frames
```

#### 2. Memory Issues
```
Error: CUDA out of memory
Solution: Reduce --batch_size or --workers
```

#### 3. Slow Training
```
Issue: Training takes much longer
Expected: ~2x training time due to dataset size increase
```

### Performance Tips
- Use `--workers 4` for faster data loading
- Monitor GPU memory usage
- Consider mixed precision training with `--fp16`

## 📚 Related Files

- `datasets/frame_loader_with_masks.py` - Core implementation
- `train_sdcnet_with_masks.py` - Training script with skip support
- `test_skip_augmentation.py` - Testing and validation
- `train_with_skip_augmentation_example.py` - Usage example

## 🎉 Success Metrics

After implementing skip augmentation, expect:
- ✅ Improved performance on fast motion videos
- ✅ Better temporal consistency in predictions
- ✅ More robust handling of challenging scenarios
- ✅ Approximately doubled training dataset size

The skip augmentation feature is designed to be backward compatible - existing training scripts will work unchanged, and the new functionality is opt-in via the `--skip_augmentation` flag.
