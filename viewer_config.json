{"title": "SDCNet Results Comparison Viewer", "description": "Visual comparison of SDCNet variants: Original vs Masked, No Skip vs Skip Training", "paths": {"dataset_test_x": "dataset/test/X", "dataset_test_y": "dataset/test/Y", "test_results": "test_results"}, "model_variants": {"groundTruth": {"title": "Ground Truth", "path_template": "{dataset_test_y}/{video_name}/{frame_number:05d}.png", "description": "Original target frames from the dataset"}, "originalNoSkip": {"title": "SDCNet Original (No Skip)", "path_template": "{test_results}/{inference_dir}/{video_name}/{frame_number:05d}.png", "description": "Original SDCNet trained without frame skipping"}, "originalSkip": {"title": "SDCNet Original (<PERSON> <PERSON><PERSON>)", "path_template": "{test_results}/{inference_dir}_skip/{video_name}/{frame_number:05d}.png", "description": "Original SDCNet trained with frame skipping augmentation"}, "maskedNoSkip": {"title": "SDCNet Masked (No Skip)", "path_template": "{test_results}/{inference_dir}_masked/{video_name}/pred_{frame_number:05d}.png", "description": "SDCNet with binary masks, trained without frame skipping"}, "maskedSkip": {"title": "SDCNet Masked (<PERSON> <PERSON><PERSON>)", "path_template": "{test_results}/{inference_dir}_masked_skip/{video_name}/pred_{frame_number:05d}.png", "description": "SDCNet with binary masks, trained with frame skipping augmentation"}}, "default_settings": {"grid_size": 1, "grid_color": "#ff0000", "frame_range": {"start": 2, "end": 89}}, "video_list": ["bmx-bumps_1", "bmx-bumps_2", "bmx-trees_1", "bmx-trees_2", "cat-girl_1", "cat-girl_2", "drift-straight", "swing", "drone_1", "drone_2", "drone_3", "drone_4", "drone_5", "gold-fish_1", "gold-fish_2", "gold-fish_3", "gold-fish_4", "gold-fish_5", "schoolgirls_1", "schoolgirls_2", "schoolgirls_3", "schoolgirls_4", "schoolgirls_5", "schoolgirls_6", "schoolgirls_7", "skate-park_1", "skate-park_2", "stunt_1", "stunt_2", "surf_1", "surf_2", "surf_3", "upside-down_1", "upside-down_2"], "inference_results": [{"id": "inference_2025_06_05_00_01_49", "display_name": "2025-06-05 00:01:49", "description": "Latest inference results"}, {"id": "inference_2025_06_04_14_42_14", "display_name": "2025-06-04 14:42:14", "description": "Previous inference results"}, {"id": "inference_2025_05_29_01_48_30", "display_name": "2025-05-29 01:48:30", "description": "Older inference results"}], "ui_settings": {"max_grid_size": 10, "default_colors": ["#ff0000", "#00ff00", "#0000ff", "#ffff00", "#ff00ff", "#00ffff", "#ffffff"], "keyboard_shortcuts": {"previous_frame": "ArrowLeft", "next_frame": "ArrowRight", "toggle_grid": "g", "reset_view": "r"}}, "error_messages": {"video_not_found": "Video not found in the dataset", "inference_not_found": "Inference results not found", "frame_not_found": "Frame not available", "loading_error": "Error loading data", "network_error": "Network connection error"}}