#!/usr/bin/env python3
"""
Quick start script for SDCNet Comparison Viewer
Checks dependencies and starts the server with proper configuration
"""

import os
import sys
import json
import webbrowser
import time
from pathlib import Path

def check_dependencies():
    """Check if required files and directories exist"""
    print("🔍 Checking dependencies...")
    
    required_files = [
        'sdcnet_comparison_viewer.html',
        'viewer_server.py',
        'viewer_config.json'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ Missing required files: {', '.join(missing_files)}")
        return False
    
    print("✅ All required files found")
    return True

def check_directories():
    """Check if data directories exist"""
    print("📁 Checking data directories...")
    
    try:
        with open('viewer_config.json', 'r') as f:
            config = json.load(f)
    except Exception as e:
        print(f"❌ Error reading config: {e}")
        return False
    
    paths = config.get('paths', {})
    
    # Check dataset directory
    dataset_test_y = paths.get('dataset_test_y', 'dataset/test/Y')
    if os.path.exists(dataset_test_y):
        video_count = len([d for d in os.listdir(dataset_test_y) 
                          if os.path.isdir(os.path.join(dataset_test_y, d))])
        print(f"✅ Dataset found: {video_count} videos in {dataset_test_y}")
    else:
        print(f"⚠️  Dataset directory not found: {dataset_test_y}")
        print("   The viewer will still work but may not show all videos")
    
    # Check test results directory
    test_results = paths.get('test_results', 'test_results')
    if os.path.exists(test_results):
        inference_count = len([d for d in os.listdir(test_results) 
                              if os.path.isdir(os.path.join(test_results, d)) 
                              and d.startswith('inference_')])
        print(f"✅ Test results found: {inference_count} inference directories in {test_results}")
    else:
        print(f"⚠️  Test results directory not found: {test_results}")
        print("   The viewer will still work but may not show inference results")
    
    return True

def get_available_port(start_port=8000):
    """Find an available port starting from start_port"""
    import socket
    
    for port in range(start_port, start_port + 100):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return port
        except OSError:
            continue
    
    return None

def start_server(port=8000):
    """Start the viewer server"""
    print(f"🚀 Starting SDCNet Comparison Viewer...")
    print(f"📡 Server will run on http://localhost:{port}")
    print(f"🎬 Viewer URL: http://localhost:{port}/sdcnet_comparison_viewer.html")
    print()
    
    # Import and start the server
    try:
        from viewer_server import run_server
        
        # Give user a moment to read the info
        print("⏳ Starting in 3 seconds... (Press Ctrl+C to cancel)")
        time.sleep(3)
        
        # Try to open browser automatically
        try:
            viewer_url = f"http://localhost:{port}/sdcnet_comparison_viewer.html"
            print(f"🌐 Opening browser to {viewer_url}")
            webbrowser.open(viewer_url)
        except Exception as e:
            print(f"⚠️  Could not open browser automatically: {e}")
            print(f"   Please open http://localhost:{port}/sdcnet_comparison_viewer.html manually")
        
        # Start server
        run_server(port)
        
    except ImportError as e:
        print(f"❌ Error importing server: {e}")
        return False
    except KeyboardInterrupt:
        print("\n🛑 Startup cancelled by user")
        return False
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        return False

def main():
    """Main function"""
    print("🎬 SDCNet Comparison Viewer - Quick Start")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists('sdcnet_comparison_viewer.html'):
        print("❌ Please run this script from the SDCNet project directory")
        print("   (the directory containing sdcnet_comparison_viewer.html)")
        sys.exit(1)
    
    # Check dependencies
    if not check_dependencies():
        print("\n❌ Dependency check failed. Please ensure all required files are present.")
        sys.exit(1)
    
    # Check directories
    check_directories()
    
    # Get port from command line or find available port
    port = 8000
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print(f"⚠️  Invalid port '{sys.argv[1]}', using default port 8000")
            port = 8000
    
    # Check if port is available
    available_port = get_available_port(port)
    if available_port != port:
        if available_port:
            print(f"⚠️  Port {port} is busy, using port {available_port} instead")
            port = available_port
        else:
            print(f"❌ No available ports found starting from {port}")
            sys.exit(1)
    
    print("\n" + "=" * 50)
    
    # Start the server
    start_server(port)

if __name__ == '__main__':
    main()
